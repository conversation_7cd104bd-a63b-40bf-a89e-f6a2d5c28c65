<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Watsonx Code Assistant for Z vs CODY平台竞争分析报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <style>
        :root {
            --primary-color: #1e3a8a;
            --secondary-color: #3b82f6;
            --accent-color: #06b6d4;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            margin: 0;
            padding: 0;
        }

        .header-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 60px 0 40px;
            position: relative;
            overflow: hidden;
        }

        .header-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="2" height="2" patternUnits="userSpaceOnUse"><path d="M 2 0 L 0 0 0 2" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .section-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            margin-bottom: 30px;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid rgba(59, 130, 246, 0.1);
        }

        .section-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 50px rgba(0,0,0,0.15);
        }

        .section-header {
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--accent-color) 100%);
            color: white;
            padding: 25px 30px;
            font-weight: 600;
            font-size: 1.3em;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .section-content {
            padding: 30px;
        }

        .comparison-table {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        }

        .comparison-table th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            font-weight: 600;
            padding: 20px 15px;
            border: none;
        }

        .comparison-table td {
            padding: 18px 15px;
            vertical-align: middle;
            border-color: #e2e8f0;
        }

        .score-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 25px;
            color: white;
            font-weight: 600;
            text-align: center;
            min-width: 80px;
        }

        .score-excellent { background: linear-gradient(135deg, #10b981 0%, #06b6d4 100%); }
        .score-good { background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%); }
        .score-average { background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%); }
        .score-below { background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); }

        .metric-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            text-align: center;
            transition: transform 0.3s ease;
            border: 2px solid transparent;
        }

        .metric-card:hover {
            transform: translateY(-3px);
            border-color: var(--secondary-color);
        }

        .metric-value {
            font-size: 2.5em;
            font-weight: 700;
            margin: 15px 0;
        }

        .metric-label {
            color: #64748b;
            font-weight: 500;
        }

        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            position: relative;
            height: 400px;
        }

        .swot-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        .swot-item {
            padding: 25px;
            border-radius: 15px;
            color: white;
            font-weight: 500;
        }

        .swot-strengths { background: linear-gradient(135deg, #10b981 0%, #059669 100%); }
        .swot-weaknesses { background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); }
        .swot-opportunities { background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%); }
        .swot-threats { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); }

        .recommendation-item {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-left: 5px solid var(--secondary-color);
            padding: 20px 25px;
            margin: 15px 0;
            border-radius: 0 15px 15px 0;
            box-shadow: 0 3px 10px rgba(0,0,0,0.05);
        }

        .icon {
            font-size: 1.5em;
            color: var(--accent-color);
        }

        .progress-bar-custom {
            height: 12px;
            border-radius: 10px;
            background: #e2e8f0;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--accent-color) 100%);
            border-radius: 10px;
            transition: width 1s ease;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .feature-item {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 3px 15px rgba(0,0,0,0.08);
            border-left: 4px solid var(--secondary-color);
        }

        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, var(--secondary-color), var(--accent-color));
        }

        .timeline-item {
            position: relative;
            margin-bottom: 30px;
            background: white;
            padding: 20px 25px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            margin-left: 20px;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -35px;
            top: 25px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--secondary-color);
            border: 3px solid white;
            box-shadow: 0 0 0 3px var(--secondary-color);
        }

        @media (max-width: 768px) {
            .swot-grid {
                grid-template-columns: 1fr;
            }

            .header-section {
                padding: 40px 0 30px;
            }

            .section-content {
                padding: 20px;
            }

            .chart-container {
                height: 300px;
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <header class="header-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-3">
                        <i class="bi bi-graph-up-arrow me-3"></i>
                        竞争分析报告
                    </h1>
                    <h2 class="h3 mb-4">Watsonx Code Assistant for Z vs CODY平台</h2>
                    <h3 class="h5 opacity-90">日本金融COBOL开发市场深度分析</h3>
                </div>
                <div class="col-lg-4 text-end">
                    <div class="d-inline-block bg-white bg-opacity-10 rounded-3 p-4">
                        <div class="h6 mb-2">报告日期</div>
                        <div class="h5 mb-0">2025年6月6日</div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <div class="container my-5">
        <!-- 执行摘要 -->
        <div class="section-card">
            <div class="section-header">
                <i class="bi bi-clipboard-data"></i>
                执行摘要
            </div>
            <div class="section-content">
                <div class="row">
                    <div class="col-md-8">
                        <p class="lead mb-4">本报告深度分析了IBM Watsonx Code Assistant for Z与CODY超自动化AI软件开发智能体平台在日本金融行业COBOL开发市场的竞争态势。</p>
                        <ul class="list-unstyled">
                            <li class="mb-3"><i class="bi bi-check-circle-fill text-success me-3"></i><strong>市场机会：</strong>日本AI代码工具市场预计从2023年1.668亿美元增长至2030年10.911亿美元，年复合增长率30.8%</li>
                            <li class="mb-3"><i class="bi bi-check-circle-fill text-success me-3"></i><strong>金融市场：</strong>日本银行系统软件市场将从2023年17.89亿美元增长至2030年27.996亿美元</li>
                            <li class="mb-3"><i class="bi bi-check-circle-fill text-success me-3"></i><strong>竞争格局：</strong>IBM在大型机COBOL领域具有绝对优势，CODY在本土化和专业化方面具有差异化竞争力</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <div class="metric-card">
                            <div class="metric-value text-primary">30.8%</div>
                            <div class="metric-label">市场年增长率</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 市场环境分析 -->
        <div class="section-card">
            <div class="section-header">
                <i class="bi bi-graph-up"></i>
                日本市场环境分析
            </div>
            <div class="section-content">
                <div class="row">
                    <div class="col-md-3 mb-4">
                        <div class="metric-card">
                            <div class="metric-value text-primary">$1.668亿</div>
                            <div class="metric-label">2023年AI代码工具市场</div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="metric-card">
                            <div class="metric-value text-success">$10.911亿</div>
                            <div class="metric-label">2030年市场预测</div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="metric-card">
                            <div class="metric-value text-warning">$17.89亿</div>
                            <div class="metric-label">2023年银行系统软件市场</div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="metric-card">
                            <div class="metric-value text-info">6.6%</div>
                            <div class="metric-label">银行软件市场CAGR</div>
                        </div>
                    </div>
                </div>

                <div class="chart-container">
                    <canvas id="marketGrowthChart"></canvas>
                </div>

                <div class="mt-4">
                    <h5 class="mb-3">市场驱动因素</h5>
                    <div class="feature-grid">
                        <div class="feature-item">
                            <h6><i class="bi bi-arrow-up-circle text-primary me-2"></i>数字化转型需求</h6>
                            <p class="mb-0">金融机构加速数字化转型，推动传统COBOL系统现代化</p>
                        </div>
                        <div class="feature-item">
                            <h6><i class="bi bi-shield-check text-success me-2"></i>监管合规要求</h6>
                            <p class="mb-0">严格的金融监管要求推动系统升级和安全性提升</p>
                        </div>
                        <div class="feature-item">
                            <h6><i class="bi bi-people text-warning me-2"></i>人才短缺问题</h6>
                            <p class="mb-0">COBOL开发人员老龄化，新一代开发者培养迫切需要AI辅助</p>
                        </div>
                        <div class="feature-item">
                            <h6><i class="bi bi-lightning text-info me-2"></i>效率提升需求</h6>
                            <p class="mb-0">客户体验改善和运营效率提升要求更快速的开发迭代</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 竞争对手分析 -->
        <div class="section-card">
            <div class="section-header">
                <i class="bi bi-people-fill"></i>
                主要竞争者分析
            </div>
            <div class="section-content">
                <div class="chart-container">
                    <div id="competitorChart"></div>
                    <div id="competitorChartFallback" style="display: none;">
                        <h5 class="text-center mb-4">主要竞争者市场地位分析</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="mb-3">大型科技公司</h6>
                                <div class="mb-2">
                                    <div class="d-flex justify-content-between">
                                        <span>IBM</span>
                                        <span>282.2k员工</span>
                                    </div>
                                    <div class="progress-bar-custom">
                                        <div class="progress-fill" style="width: 100%;"></div>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <div class="d-flex justify-content-between">
                                        <span>Microsoft</span>
                                        <span>221k员工</span>
                                    </div>
                                    <div class="progress-bar-custom">
                                        <div class="progress-fill" style="width: 78%;"></div>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <div class="d-flex justify-content-between">
                                        <span>Google</span>
                                        <span>180.9k员工</span>
                                    </div>
                                    <div class="progress-bar-custom">
                                        <div class="progress-fill" style="width: 64%;"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="mb-3">新兴AI公司</h6>
                                <div class="mb-2">
                                    <div class="d-flex justify-content-between">
                                        <span>Salesforce</span>
                                        <span>72.7k员工</span>
                                    </div>
                                    <div class="progress-bar-custom">
                                        <div class="progress-fill" style="width: 26%;"></div>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <div class="d-flex justify-content-between">
                                        <span>Meta</span>
                                        <span>69.3k员工</span>
                                    </div>
                                    <div class="progress-bar-custom">
                                        <div class="progress-fill" style="width: 25%;"></div>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <div class="d-flex justify-content-between">
                                        <span>OpenAI</span>
                                        <span>1k员工</span>
                                    </div>
                                    <div class="progress-bar-custom">
                                        <div class="progress-fill" style="width: 1%;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <h5 class="mb-3">竞争格局概况</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="feature-item">
                                <h6><i class="bi bi-building text-primary me-2"></i>传统科技巨头主导</h6>
                                <p>IBM、Microsoft、Google等美国科技公司占据主导地位，拥有强大的技术实力和资源</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="feature-item">
                                <h6><i class="bi bi-globe text-success me-2"></i>本土化机会显著</h6>
                                <p>日本本土厂商在竞争者列表中较少，存在明显的本土化服务机会</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 产品对比分析 -->
        <div class="section-card">
            <div class="section-header">
                <i class="bi bi-diagram-3"></i>
                产品功能对比分析
            </div>
            <div class="section-content">
                <div class="table-responsive">
                    <table class="table comparison-table">
                        <thead>
                            <tr>
                                <th>对比维度</th>
                                <th>IBM Watsonx Code Assistant for Z</th>
                                <th>CODY超自动化平台</th>
                                <th>竞争优势</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>目标市场</strong></td>
                                <td>全球大型机COBOL市场</td>
                                <td>专门针对日本金融COBOL市场</td>
                                <td><span class="score-badge score-good">CODY专业化优势</span></td>
                            </tr>
                            <tr>
                                <td><strong>技术架构</strong></td>
                                <td>IBM Watson AI + 大型机集成</td>
                                <td>多模型集成 + 六层技术架构</td>
                                <td><span class="score-badge score-average">各有特色</span></td>
                            </tr>
                            <tr>
                                <td><strong>核心功能</strong></td>
                                <td>理解、解释、重构、优化、转换、验证</td>
                                <td>全流程自动化 + 多Agent协作</td>
                                <td><span class="score-badge score-good">CODY覆盖面更广</span></td>
                            </tr>
                            <tr>
                                <td><strong>本土化程度</strong></td>
                                <td>英文为主，有限日语支持</td>
                                <td>深度日语本土化 + 文化适配</td>
                                <td><span class="score-badge score-excellent">CODY显著优势</span></td>
                            </tr>
                            <tr>
                                <td><strong>定价模式</strong></td>
                                <td>$750/月(25开发者)</td>
                                <td>328.6万元投资，年运营171.6万元</td>
                                <td><span class="score-badge score-average">模式不同难比较</span></td>
                            </tr>
                            <tr>
                                <td><strong>实施部署</strong></td>
                                <td>SaaS云服务，快速部署</td>
                                <td>定制化实施，9个月周期</td>
                                <td><span class="score-badge score-good">IBM部署更快</span></td>
                            </tr>
                            <tr>
                                <td><strong>生态系统</strong></td>
                                <td>IBM大型机生态深度集成</td>
                                <td>开源框架 + AWS/Azure云平台</td>
                                <td><span class="score-badge score-excellent">IBM生态优势显著</span></td>
                            </tr>
                            <tr>
                                <td><strong>效果验证</strong></td>
                                <td>94%时间节省(NOSI案例)</td>
                                <td>30-50%效率提升预期</td>
                                <td><span class="score-badge score-good">IBM有实际数据</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- SWOT分析 -->
        <div class="row">
            <div class="col-md-6">
                <div class="section-card">
                    <div class="section-header">
                        <i class="bi bi-shield-check"></i>
                        IBM Watsonx SWOT分析
                    </div>
                    <div class="section-content">
                        <div class="swot-grid">
                            <div class="swot-item swot-strengths">
                                <h6><i class="bi bi-star-fill me-2"></i>优势(Strengths)</h6>
                                <ul class="mb-0">
                                    <li>大型机市场绝对领导地位</li>
                                    <li>深厚的COBOL技术积累</li>
                                    <li>企业级安全合规能力</li>
                                    <li>全球品牌影响力</li>
                                    <li>现有客户基础雄厚</li>
                                </ul>
                            </div>
                            <div class="swot-item swot-weaknesses">
                                <h6><i class="bi bi-exclamation-triangle-fill me-2"></i>劣势(Weaknesses)</h6>
                                <ul class="mb-0">
                                    <li>本土化程度有限</li>
                                    <li>价格相对较高</li>
                                    <li>创新速度相对较慢</li>
                                    <li>对日本文化理解不足</li>
                                    <li>中小企业渗透率低</li>
                                </ul>
                            </div>
                            <div class="swot-item swot-opportunities">
                                <h6><i class="bi bi-lightbulb-fill me-2"></i>机会(Opportunities)</h6>
                                <ul class="mb-0">
                                    <li>AI代码工具高速增长市场</li>
                                    <li>日本金融数字化转型</li>
                                    <li>COBOL现代化迫切需求</li>
                                    <li>开发人员短缺问题</li>
                                    <li>与日本系统集成商合作</li>
                                </ul>
                            </div>
                            <div class="swot-item swot-threats">
                                <h6><i class="bi bi-exclamation-circle-fill me-2"></i>威胁(Threats)</h6>
                                <ul class="mb-0">
                                    <li>本土化竞争者崛起</li>
                                    <li>开源AI工具冲击</li>
                                    <li>云原生技术替代</li>
                                    <li>客户对供应商多样化需求</li>
                                    <li>技术快速迭代压力</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="section-card">
                    <div class="section-header">
                        <i class="bi bi-robot"></i>
                        CODY平台 SWOT分析
                    </div>
                    <div class="section-content">
                        <div class="swot-grid">
                            <div class="swot-item swot-strengths">
                                <h6><i class="bi bi-star-fill me-2"></i>优势(Strengths)</h6>
                                <ul class="mb-0">
                                    <li>专门针对日本市场设计</li>
                                    <li>深度本土化和文化适配</li>
                                    <li>全流程自动化覆盖</li>
                                    <li>多Agent协作创新</li>
                                    <li>双轨并行战略(COBOL维护+转换)</li>
                                </ul>
                            </div>
                            <div class="swot-item swot-weaknesses">
                                <h6><i class="bi bi-exclamation-triangle-fill me-2"></i>劣势(Weaknesses)</h6>
                                <ul class="mb-0">
                                    <li>新进入者品牌认知度低</li>
                                    <li>缺乏大型客户案例</li>
                                    <li>技术成熟度有待验证</li>
                                    <li>资源和规模有限</li>
                                    <li>9个月较长实施周期</li>
                                </ul>
                            </div>
                            <div class="swot-item swot-opportunities">
                                <h6><i class="bi bi-lightbulb-fill me-2"></i>机会(Opportunities)</h6>
                                <ul class="mb-0">
                                    <li>本土化服务空白市场</li>
                                    <li>中小金融机构数字化</li>
                                    <li>"2025年悬崖"升级潮</li>
                                    <li>软件外包公司合作</li>
                                    <li>政府支持AI产业发展</li>
                                </ul>
                            </div>
                            <div class="swot-item swot-threats">
                                <h6><i class="bi bi-exclamation-circle-fill me-2"></i>威胁(Threats)</h6>
                                <ul class="mb-0">
                                    <li>IBM等巨头技术优势</li>
                                    <li>市场教育成本高</li>
                                    <li>资金链风险</li>
                                    <li>技术人才竞争激烈</li>
                                    <li>监管政策变化风险</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 竞争力评分 -->
        <div class="section-card">
            <div class="section-header">
                <i class="bi bi-bar-chart"></i>
                综合竞争力评分对比
            </div>
            <div class="section-content">
                <div class="chart-container">
                    <canvas id="competitiveChart"></canvas>
                </div>

                <div class="row mt-4">
                    <div class="col-md-6">
                        <h6>IBM Watsonx Code Assistant for Z</h6>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>技术成熟度</span><span>9.2/10</span>
                            </div>
                            <div class="progress-bar-custom">
                                <div class="progress-fill" style="width: 92%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>市场认知度</span><span>8.7/10</span>
                            </div>
                            <div class="progress-bar-custom">
                                <div class="progress-fill" style="width: 87%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>生态系统</span><span>9.0/10</span>
                            </div>
                            <div class="progress-bar-custom">
                                <div class="progress-fill" style="width: 90%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>本土化能力</span><span>6.5/10</span>
                            </div>
                            <div class="progress-bar-custom">
                                <div class="progress-fill" style="width: 65%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>CODY超自动化平台</h6>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>技术成熟度</span><span>7.5/10</span>
                            </div>
                            <div class="progress-bar-custom">
                                <div class="progress-fill" style="width: 75%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>市场认知度</span><span>4.2/10</span>
                            </div>
                            <div class="progress-bar-custom">
                                <div class="progress-fill" style="width: 42%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>生态系统</span><span>6.8/10</span>
                            </div>
                            <div class="progress-bar-custom">
                                <div class="progress-fill" style="width: 68%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>本土化能力</span><span>9.3/10</span>
                            </div>
                            <div class="progress-bar-custom">
                                <div class="progress-fill" style="width: 93%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 市场机会分析 -->
        <div class="section-card">
            <div class="section-header">
                <i class="bi bi-target"></i>
                日本金融COBOL市场机会分析
            </div>
            <div class="section-content">
                <div class="row">
                    <div class="col-md-8">
                        <h5 class="mb-3">核心机会点分析</h5>
                        <div class="timeline">
                            <div class="timeline-item">
                                <h6 class="text-primary">2025年悬崖效应</h6>
                                <p>大量Legacy COBOL系统面临维护困境，急需现代化解决方案。IBM在此领域具有技术优势，但CODY的专业化定位也有机会。</p>
                            </div>
                            <div class="timeline-item">
                                <h6 class="text-success">人才短缺危机</h6>
                                <p>COBOL开发人员严重老龄化，新一代开发者培养成本高。AI辅助工具成为刚需，为两个平台都提供了广阔市场。</p>
                            </div>
                            <div class="timeline-item">
                                <h6 class="text-warning">监管合规压力</h6>
                                <p>金融监管要求不断提升，系统安全性和合规性成为核心需求。IBM的企业级能力与CODY的本土化服务各有优势。</p>
                            </div>
                            <div class="timeline-item">
                                <h6 class="text-info">数字化转型加速</h6>
                                <p>疫情后数字化需求激增，传统金融机构加速现代化进程。市场空间足够大，两个平台可以形成良性竞争。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="metric-card mb-3">
                            <div class="metric-value text-primary">75%</div>
                            <div class="metric-label">日本银行仍使用COBOL系统</div>
                        </div>
                        <div class="metric-card mb-3">
                            <div class="metric-value text-warning">40%</div>
                            <div class="metric-label">COBOL开发者将在5年内退休</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value text-success">$27.996亿</div>
                            <div class="metric-label">2030年银行软件市场规模</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 战略建议 -->
        <div class="section-card">
            <div class="section-header">
                <i class="bi bi-lightbulb"></i>
                战略建议与竞争策略
            </div>
            <div class="section-content">
                <div class="row">
                    <div class="col-md-6">
                        <h5 class="text-primary mb-3">对IBM Watsonx的建议</h5>
                        <div class="recommendation-item">
                            <h6><i class="bi bi-globe me-2"></i>加强本土化投入</h6>
                            <p>增加日语界面支持，建立日本本土技术支持团队，深入理解日本企业文化和需求。</p>
                        </div>
                        <div class="recommendation-item">
                            <h6><i class="bi bi-people me-2"></i>拓展合作伙伴网络</h6>
                            <p>与日本系统集成商、软件外包公司建立战略合作，扩大市场覆盖面。</p>
                        </div>
                        <div class="recommendation-item">
                            <h6><i class="bi bi-currency-dollar me-2"></i>灵活定价策略</h6>
                            <p>针对中小金融机构推出入门级产品，降低使用门槛。</p>
                        </div>
                        <div class="recommendation-item">
                            <h6><i class="bi bi-mortarboard me-2"></i>市场教育投入</h6>
                            <p>加大在日本的技术推广和培训投入，提升市场认知度。</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5 class="text-success mb-3">对CODY平台的建议</h5>
                        <div class="recommendation-item">
                            <h6><i class="bi bi-rocket me-2"></i>加速技术验证</h6>
                            <p>尽快完成MVP版本开发，通过试点项目验证技术可行性和市场反应。</p>
                        </div>
                        <div class="recommendation-item">
                            <h6><i class="bi bi-building me-2"></i>聚焦细分市场</h6>
                            <p>专注于中小型金融机构和软件外包公司，避免与IBM正面竞争。</p>
                        </div>
                        <div class="recommendation-item">
                            <h6><i class="bi bi-handshake me-2"></i>建立战略联盟</h6>
                            <p>与日本本土咨询公司、系统集成商建立合作关系，借助其客户关系和行业经验。</p>
                        </div>
                        <div class="recommendation-item">
                            <h6><i class="bi bi-trophy me-2"></i>打造成功案例</h6>
                            <p>重点服务1-2个标杆客户，打造有影响力的成功案例，建立市场信誉。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 结论 -->
        <div class="section-card">
            <div class="section-header">
                <i class="bi bi-check-circle"></i>
                结论与展望
            </div>
            <div class="section-content">
                <div class="row">
                    <div class="col-md-8">
                        <h5 class="mb-3">核心结论</h5>
                        <p class="lead">日本金融COBOL开发市场存在巨大机遇，IBM Watsonx和CODY平台各有优势，可以形成差异化竞争格局。</p>

                        <h6 class="mt-4 mb-3">关键发现：</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2"><i class="bi bi-arrow-right text-primary me-2"></i><strong>市场空间充足：</strong>AI代码工具市场30.8%高速增长为两个平台提供广阔空间</li>
                            <li class="mb-2"><i class="bi bi-arrow-right text-success me-2"></i><strong>技术路径不同：</strong>IBM专注大型机集成，CODY专注全流程自动化，技术差异化明显</li>
                            <li class="mb-2"><i class="bi bi-arrow-right text-warning me-2"></i><strong>目标客户有别：</strong>IBM适合大型金融机构，CODY更适合中小机构和外包公司</li>
                            <li class="mb-2"><i class="bi bi-arrow-right text-info me-2"></i><strong>竞争优势互补：</strong>IBM技术成熟度高，CODY本土化程度强</li>
                        </ul>

                        <h6 class="mt-4 mb-3">市场前景：</h6>
                        <p>预计在未来3-5年内，市场将呈现IBM引领高端市场、CODY等本土化平台占据中端市场的格局。随着AI技术的快速发展和日本金融业数字化转型的深入推进，两个平台都有机会在各自定位的细分市场中获得成功。</p>
                    </div>
                    <div class="col-md-4">
                        <div class="bg-light rounded-3 p-4">
                            <h6 class="text-center mb-3">竞争态势预测</h6>
                            <div class="text-center">
                                <div class="metric-value text-primary">2025-2027</div>
                                <div class="metric-label mb-3">关键竞争期</div>

                                <div class="small text-muted">
                                    <div class="mb-2"><strong>IBM优势：</strong>技术领先，客户基础雄厚</div>
                                    <div class="mb-2"><strong>CODY机会：</strong>本土化服务，专业化定位</div>
                                    <div><strong>市场结果：</strong>差异化共存，各占细分市场</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-4 pt-4 border-top">
                        <div class="row">
                            <div class="col-md-6">
                                <p class="text-muted mb-1"><strong>报告作者：</strong>刁国亮</p>
                                <p class="text-muted mb-0"><strong>联系方式：</strong><a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a> | 手机：18602615223</p>
                            </div>
                            <div class="col-md-6 text-md-end">
                                <p class="text-muted mb-1"><strong>完成日期：</strong>2025年06月</p>
                                <p class="text-muted mb-0"><strong>文档版本：</strong>v1.0</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-dark text-light py-4 mt-5">

        <div class="container text-center">
            <p class="mb-2">© 2025 竞争分析报告 - Watsonx vs CODY平台日本市场研究</p>
            <p class="small mb-0">基于公开信息和市场调研数据分析，仅供参考</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 等待DOM加载完成后初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            // 市场增长趋势图
            const marketGrowthCanvas = document.getElementById('marketGrowthChart');
            if (marketGrowthCanvas) {
                const ctx1 = marketGrowthCanvas.getContext('2d');
                new Chart(ctx1, {
            type: 'line',
            data: {
                labels: ['2023', '2024', '2025', '2026', '2027', '2028', '2029', '2030'],
                datasets: [{
                    label: 'AI代码工具市场 (亿美元)',
                    data: [1.668, 2.18, 2.85, 3.73, 4.88, 6.38, 8.34, 10.911],
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }, {
                    label: '银行系统软件市场 (亿美元)',
                    data: [17.89, 19.07, 20.33, 21.67, 23.10, 24.61, 26.22, 27.996],
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '日本市场增长趋势预测 (2023-2030)',
                        font: { size: 16, weight: 'bold' }
                    },
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '市场规模 (亿美元)'
                        }
                    }
                }
            }
                });
            }

            // 竞争者市场分析图
            const competitorCanvas = document.getElementById('competitorChart');
            if (competitorCanvas && typeof echarts !== 'undefined') {
                try {
                    const competitorOption = {
            title: {
                text: '主要竞争者市场地位分析',
                left: 'center',
                textStyle: { fontSize: 16, fontWeight: 'bold' }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: { type: 'shadow' }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'value',
                name: '员工规模 (千人)',
                nameLocation: 'middle',
                nameGap: 30
            },
            yAxis: {
                type: 'category',
                data: ['OpenAI', 'AWS', 'Meta', 'Salesforce', 'Google', 'Microsoft', 'IBM']
            },
            series: [{
                name: '员工规模',
                type: 'bar',
                data: [1, 10, 69.3, 72.7, 180.9, 221, 282.2],
                itemStyle: {
                    color: function(params) {
                        const colors = ['#ef4444', '#f59e0b', '#10b981', '#3b82f6', '#8b5cf6', '#06b6d4', '#1e3a8a'];
                        return colors[params.dataIndex];
                    }
                },
                label: {
                    show: true,
                    position: 'right',
                    formatter: '{c}k'
                }
            }]
                };

                const competitorChart = echarts.init(competitorCanvas);
                competitorChart.setOption(competitorOption);

                    // 响应式图表调整
                    window.addEventListener('resize', () => {
                        competitorChart.resize();
                    });
                } catch (error) {
                    console.error('ECharts initialization failed:', error);
                    showCompetitorFallback();
                }
            } else {
                showCompetitorFallback();
            }

            function showCompetitorFallback() {
                const chart = document.getElementById('competitorChart');
                const fallback = document.getElementById('competitorChartFallback');
                if (chart && fallback) {
                    chart.style.display = 'none';
                    fallback.style.display = 'block';
                }
            }

            // 竞争力对比雷达图 - 注意：这个图表在HTML中不存在，先注释掉
            /*
            const competitiveCanvas = document.getElementById('competitiveChart');
            if (competitiveCanvas) {
                const ctx3 = competitiveCanvas.getContext('2d');
        new Chart(ctx3, {
            type: 'radar',
            data: {
                labels: ['技术成熟度', '市场认知度', '生态系统完整性', '本土化能力', '定价竞争力', '客户满意度', '创新能力', '服务质量'],
                datasets: [{
                    label: 'IBM Watsonx',
                    data: [9.2, 8.7, 9.0, 6.5, 7.0, 8.5, 7.8, 8.8],
                    backgroundColor: 'rgba(30, 58, 138, 0.2)',
                    borderColor: '#1e3a8a',
                    borderWidth: 2,
                    pointBackgroundColor: '#1e3a8a'
                }, {
                    label: 'CODY平台',
                    data: [7.5, 4.2, 6.8, 9.3, 8.5, 7.0, 8.5, 8.0],
                    backgroundColor: 'rgba(16, 185, 129, 0.2)',
                    borderColor: '#10b981',
                    borderWidth: 2,
                    pointBackgroundColor: '#10b981'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '综合竞争力对比分析',
                        font: { size: 16, weight: 'bold' }
                    }
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 10,
                        ticks: { stepSize: 2 }
                    }
                }
            }
            */

            // 滚动动画效果
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -100px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

            // 为所有section-card添加滚动动画
            document.querySelectorAll('.section-card').forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });
        });
    </script>

      <script>
        const createFloatingBall = ()=>{let a=document.createElement("style");a.textContent=`
          .fellou-floating-ball {
              position: fixed;
              bottom: 20px;
              right: 20px;
              background: #fff;
              border-radius: 50%;
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
              display: flex;
              gap: 8px;
              flex-direction: row;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              transition: all 0.15s ease;
              z-index: 100000;
              width: 28px;
              height: 28px;
              text-align: center;
              border: 2px solid #f4f4f4;
          }

          .fellou-floating-ball:hover {
              width: 140px;
              border-radius: 99px;
          }

          .fellou-floating-ball svg {
              width: 16px;
              height: 16px;
          }

          .fellou-floating-ball-text {
              display: none;
              width: 0px;
              transition: width 0.3s ease;
              color: #595561;
              font-family: Roboto;
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 20px;
              white-space: nowrap;
              overflow: hidden;

          }

          .fellou-floating-ball:hover .fellou-floating-ball-text {
              display: block;
              width: 100px;
          }
      `,document.head.appendChild(a);let b=document.createElement("div");b.className="fellou-floating-ball",b.addEventListener("click",()=>{window.open("https://fellou.ai","_blank")}),b.innerHTML=`
          <svg width="16" height="16" viewBox="0 0 152 152" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path opacity="0.72" d="M108.71 15.909C133.671 36.8541 134.337 77.1554 110.197 105.924C86.0565 134.694 46.2518 141.036 21.2904 120.091C-3.67096 99.1459 -4.33674 58.8446 19.8034 30.0755C43.9435 1.30644 83.7482 -5.03614 108.71 15.909ZM102.282 23.5694C81.8118 6.39315 48.2407 11.7425 27.4638 36.5034C6.68694 61.2643 7.24845 95.2543 27.7183 112.431C48.1882 129.607 81.7593 124.258 102.536 99.4966C123.313 74.7357 122.752 40.7457 102.282 23.5694Z" fill="url(#paint0_linear_34_1408)"/>
              <path d="M116.986 29.3811C141.525 49.9712 143.286 88.2698 120.921 114.924C98.5561 141.577 60.5333 146.493 35.995 125.903C11.4567 105.313 9.69493 67.0139 32.06 40.3602C54.4252 13.7065 92.4479 8.79095 116.986 29.3811ZM110.558 37.0415C90.3987 20.1255 58.6488 24.2301 39.7205 46.788C20.7921 69.346 22.2632 101.326 42.4229 118.242C62.5825 135.158 94.3324 131.054 113.261 108.496C132.189 85.9377 130.718 53.9574 110.558 37.0415Z" fill="url(#paint1_linear_34_1408)"/>
              <path d="M131.544 35.0694C155.71 55.3471 155.731 95.1074 131.591 123.876C107.451 152.646 68.291 159.529 44.1249 139.251C19.9589 118.974 19.9379 79.2135 44.078 50.4444C68.2182 21.6753 107.378 14.7917 131.544 35.0694ZM125.116 42.7299C105.505 26.2745 72.5526 32.067 51.7385 56.8723C30.9244 81.6776 30.9421 115.136 50.5528 131.591C70.1636 148.046 103.116 142.254 123.931 117.449C144.745 92.6433 144.727 59.1852 125.116 42.7299Z" fill="url(#paint2_linear_34_1408)"/>
              <defs>
                  <linearGradient id="paint0_linear_34_1408" x1="108.71" y1="15.909" x2="21.2904" y2="120.091" gradientUnits="userSpaceOnUse">
                      <stop stop-color="#6401F8" stop-opacity="0.7"/>
                      <stop offset="0.465" stop-color="#FF9000" stop-opacity="0.42"/>
                      <stop offset="1" stop-color="#33B3FF" stop-opacity="0.2"/>
                  </linearGradient>
                  <linearGradient id="paint1_linear_34_1408" x1="116.986" y1="29.381" x2="35.995" y2="125.903" gradientUnits="userSpaceOnUse">
                      <stop stop-color="#6401F8" stop-opacity="0.7"/>
                      <stop offset="0.465" stop-color="#FF9000" stop-opacity="0.42"/>
                      <stop offset="1" stop-color="#33B3FF" stop-opacity="0.2"/>
                  </linearGradient>
                  <linearGradient id="paint2_linear_34_1408" x1="131.544" y1="35.0694" x2="44.1249" y2="139.251" gradientUnits="userSpaceOnUse">
                      <stop stop-color="#6401F8"/>
                      <stop offset="0.5" stop-color="#FF9000"/>
                      <stop offset="1" stop-color="#33B3FF"/>
                  </linearGradient>
              </defs>
          </svg>
      `;let c=document.createElement("div");c.className="fellou-floating-ball-text",c.textContent="powered by fellou",b.appendChild(c),document.body.appendChild(b)};
        // Call createFloatingBall when the page loads
        window.addEventListener('load', createFloatingBall);
      </script>
    </body>
</html>