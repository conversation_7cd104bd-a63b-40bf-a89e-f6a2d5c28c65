<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Watsonx Code Assistant for Z在日本市场竞争分析报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 40px 20px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .section {
            background: white;
            margin: 20px 0;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .section:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        .section-header {
            border-left: 5px solid #667eea;
            padding-left: 20px;
            margin-bottom: 25px;
        }

        .section-header h2 {
            color: #667eea;
            font-size: 1.8em;
            margin-bottom: 5px;
        }

        .section-header .icon {
            margin-right: 10px;
            color: #764ba2;
        }

        .toc {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .toc-item {
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            padding: 20px;
            border-radius: 10px;
            color: white;
            text-decoration: none;
            transition: transform 0.3s ease;
        }

        .toc-item:hover {
            transform: scale(1.05);
            text-decoration: none;
            color: white;
        }

        .highlight-box {
            background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            color: white;
            font-weight: bold;
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .comparison-card {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            transition: border-color 0.3s ease;
        }

        .comparison-card:hover {
            border-color: #667eea;
        }

        .company-header {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            color: white;
            font-weight: bold;
        }

        .ibm-header { background: linear-gradient(45deg, #1e3c72 0%, #2a5298 100%); }
        .fujitsu-header { background: linear-gradient(45deg, #ff6b6b 0%, #ee5a24 100%); }
        .hitachi-header { background: linear-gradient(45deg, #20bf6b 0%, #01a3a4 100%); }
        .nec-header { background: linear-gradient(45deg, #8854d0 0%, #3742fa 100%); }

        .chart-container {
            height: 400px;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .metric-card {
            text-align: center;
            padding: 25px;
            border-radius: 15px;
            color: white;
            font-weight: bold;
        }

        .metric-value {
            font-size: 2em;
            margin: 10px 0;
        }

        .pricing-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
        }

        .pricing-table th {
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: left;
        }

        .pricing-table td {
            padding: 15px;
            border-bottom: 1px solid #e0e0e0;
        }

        .pricing-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .swot-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 20px 0;
        }

        .swot-item {
            padding: 20px;
            border-radius: 10px;
            color: white;
        }

        .strengths { background: linear-gradient(45deg, #4CAF50 0%, #8BC34A 100%); }
        .weaknesses { background: linear-gradient(45deg, #f44336 0%, #FF5722 100%); }
        .opportunities { background: linear-gradient(45deg, #2196F3 0%, #03A9F4 100%); }
        .threats { background: linear-gradient(45deg, #9C27B0 0%, #E91E63 100%); }

        .recommendation-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
        }

        .timeline {
            position: relative;
            margin: 30px 0;
        }

        .timeline-item {
            display: flex;
            margin: 20px 0;
            position: relative;
        }

        .timeline-marker {
            width: 20px;
            height: 20px;
            background: #667eea;
            border-radius: 50%;
            margin-right: 20px;
            margin-top: 10px;
            flex-shrink: 0;
        }

        .timeline-content {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            flex: 1;
        }

        .progress-bar {
            background: #e0e0e0;
            border-radius: 10px;
            height: 20px;
            margin: 10px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            transition: width 1s ease;
        }

        .footer {
            text-align: center;
            padding: 30px;
            background: #333;
            color: white;
            border-radius: 15px;
            margin-top: 40px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 1.8em;
            }
            
            .comparison-grid {
                grid-template-columns: 1fr;
            }
            
            .swot-grid {
                grid-template-columns: 1fr;
            }
            
            .section {
                padding: 20px;
            }
        }

        .tag {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            margin: 2px;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 5px solid;
        }

        .alert-info {
            background-color: #e3f2fd;
            border-color: #2196f3;
            color: #1976d2;
        }

        .alert-warning {
            background-color: #fff3e0;
            border-color: #ff9800;
            color: #f57c00;
        }

        .alert-success {
            background-color: #e8f5e8;
            border-color: #4caf50;
            color: #388e3c;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 报告标题 -->
        <div class="header">
            <h1><i class="fas fa-chart-line"></i> Watsonx Code Assistant for Z</h1>
            <p>日本市场竞争态势深度分析报告</p>
            <div style="margin-top: 20px; font-size: 0.9em;">
                <i class="fas fa-calendar"></i> 报告日期：2025年6月6日 |
                <i class="fas fa-user"></i> 分析团队：AI市场研究部
            </div>
        </div>

        <!-- 目录 -->
        <div class="section">
            <div class="section-header">
                <h2><i class="fas fa-list icon"></i>报告目录</h2>
            </div>
            <div class="toc">
                <a href="#executive-summary" class="toc-item">
                    <i class="fas fa-chart-bar"></i> 1. 执行摘要
                </a>
                <a href="#product-overview" class="toc-item">
                    <i class="fas fa-cogs"></i> 2. 产品概述
                </a>
                <a href="#market-analysis" class="toc-item">
                    <i class="fas fa-map-marked-alt"></i> 3. 市场分析
                </a>
                <a href="#competitor-analysis" class="toc-item">
                    <i class="fas fa-users"></i> 4. 竞争对手分析
                </a>
                <a href="#opportunities" class="toc-item">
                    <i class="fas fa-lightbulb"></i> 5. 机会与挑战
                </a>
                <a href="#channel-strategy" class="toc-item">
                    <i class="fas fa-network-wired"></i> 6. 渠道策略
                </a>
                <a href="#pricing-analysis" class="toc-item">
                    <i class="fas fa-yen-sign"></i> 7. 定价策略
                </a>
                <a href="#technical-comparison" class="toc-item">
                    <i class="fas fa-code"></i> 8. 技术实现
                </a>
                <a href="#service-analysis" class="toc-item">
                    <i class="fas fa-hands-helping"></i> 9. 服务体系
                </a>
                <a href="#recommendations" class="toc-item">
                    <i class="fas fa-star"></i> 10. 建议结论
                </a>
            </div>
        </div>

        <!-- 1. 执行摘要 -->
        <div id="executive-summary" class="section">
            <div class="section-header">
                <h2><i class="fas fa-chart-bar icon"></i>执行摘要</h2>
            </div>
            
            <div class="highlight-box">
                <i class="fas fa-exclamation-triangle"></i> 
                <strong>关键发现：</strong>日本COBOL现代化市场面临巨大机遇，但本土厂商主导地位明显，IBM需要差异化策略突破
            </div>

            <div class="metrics-grid">
                <div class="metric-card" style="background: linear-gradient(45deg, #ff6b6b 0%, #ee5a24 100%);">
                    <div class="metric-value">59.5%</div>
                    <div>本土厂商市场份额</div>
                    <small>富士通24.5% + 日立18.1% + NEC16.9%</small>
                </div>
                <div class="metric-card" style="background: linear-gradient(45deg, #3742fa 0%, #2f3542 100%);">
                    <div class="metric-value">35%</div>
                    <div>AI辅助开发生产力提升</div>
                    <small>基于IDC 2024调研数据</small>
                </div>
                <div class="metric-card" style="background: linear-gradient(45deg, #20bf6b 0%, #01a3a4 100%);">
                    <div class="metric-value">70%</div>
                    <div>文档处理时间节约</div>
                    <small>AI智能体平台预期效果</small>
                </div>
                <div class="metric-card" style="background: linear-gradient(45deg, #8854d0 0%, #3d5af1 100%);">
                    <div class="metric-value">$3000</div>
                    <div>IBM月度起步价格</div>
                    <small>Standard计划（包含3000任务提示）</small>
                </div>
            </div>

            <p style="font-size: 1.1em; margin: 20px 0;">
                本报告深度分析了IBM Watsonx Code Assistant for Z在日本金融行业COBOL开发市场的竞争态势。
                研究发现，虽然日本本土厂商（富士通、日立、NEC）占据市场主导地位，但生成式AI技术的兴起为IBM提供了差异化竞争机会。
                关键成功因素包括：加强本土化适配、构建生态合作伙伴、提供差异化AI技术价值、以及灵活的定价策略。
            </p>
        </div>

        <!-- 2. 产品概述 -->
        <div id="product-overview" class="section">
            <div class="section-header">
                <h2><i class="fas fa-cogs icon"></i>IBM Watsonx Code Assistant for Z产品概述</h2>
            </div>

            <div class="comparison-grid">
                <div>
                    <h3><i class="fas fa-robot"></i> 核心功能特性</h3>
                    <ul style="margin: 15px 0; padding-left: 20px;">
                        <li><strong>生成式AI驱动：</strong>专为大型机应用程序现代化设计</li>
                        <li><strong>全生命周期支持：</strong>从发现、分析到转换、测试的完整流程</li>
                        <li><strong>COBOL专长：</strong>支持COBOL到Java的智能转换</li>
                        <li><strong>智能理解：</strong>JCL作业理解和依赖关系映射</li>
                        <li><strong>增量现代化：</strong>降低成本和风险的渐进式改造</li>
                    </ul>
                </div>
                <div>
                    <h3><i class="fas fa-brain"></i> 技术优势</h3>
                    <ul style="margin: 15px 0; padding-left: 20px;">
                        <li><strong>专用LLM：</strong>针对大型机语言微调的大语言模型</li>
                        <li><strong>混合部署：</strong>支持本地部署和云端服务</li>
                        <li><strong>语言互操作：</strong>COBOL/Java语言互操作性</li>
                        <li><strong>智能重构：</strong>动态重构和代码优化</li>
                        <li><strong>质量保证：</strong>提高代码理解、质量和准确性</li>
                    </ul>
                </div>
            </div>

            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>产品定位：</strong>Watsonx Code Assistant for Z定位为专业的大型机现代化AI助手，
                主要针对金融、保险等拥有大量遗留COBOL系统的行业，通过生成式AI技术加速现代化进程。
            </div>

            <div class="chart-container">
                <canvas id="productFeatureChart"></canvas>
            </div>
        </div>

        <!-- 3. 日本金融行业COBOL市场分析 -->
        <div id="market-analysis" class="section">
            <div class="section-header">
                <h2><i class="fas fa-map-marked-alt icon"></i>日本金融行业COBOL现代化市场分析</h2>
            </div>

            <div class="highlight-box">
                <i class="fas fa-building"></i>
                <strong>市场特征：</strong>日本金融行业严重依赖COBOL遗留系统，现代化需求迫切，但转换风险敏感度极高
            </div>

            <h3><i class="fas fa-industry"></i> 市场驱动因素</h3>
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <h4>技术债务积累</h4>
                        <p>数十年的COBOL系统维护成本持续上升，新功能开发周期长，难以适应数字化转型需求</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <h4>人才稀缺危机</h4>
                        <p>COBOL程序员年龄老化，新一代开发者缺乏相关技能，知识传承面临断层风险</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <h4>监管合规要求</h4>
                        <p>金融监管对系统稳定性、安全性要求不断提高，现代化改造势在必行</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <h4>数字化转型压力</h4>
                        <p>客户体验提升、实时交易处理、API经济等新需求推动系统现代化</p>
                    </div>
                </div>
            </div>

            <h3><i class="fas fa-chart-pie"></i> 市场份额分布</h3>
            <div class="chart-container">
                <div id="marketShareChart"></div>
            </div>

            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>挑战分析：</strong>日本金融行业的保守文化和风险规避倾向使得COBOL现代化项目推进缓慢，
                需要提供稳定可靠的渐进式解决方案。
            </div>
        </div>

        <!-- 4. 竞争对手分析 -->
        <div id="competitor-analysis" class="section">
            <div class="section-header">
                <h2><i class="fas fa-users icon"></i>主要竞争对手深度分析</h2>
            </div>

            <div class="comparison-grid">
                <div class="comparison-card">
                    <div class="company-header fujitsu-header">
                        <i class="fas fa-building"></i> 富士通 (Fujitsu)
                    </div>
                    <div style="margin: 15px 0;">
                        <span class="tag">市场领导者</span>
                        <span class="tag">24.5%份额</span>
                        <span class="tag">本土优势</span>
                    </div>
                    <h4>核心产品：PROGRESSION服务</h4>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>自动COBOL到Java/C#转换</li>
                        <li>50+全球成功案例</li>
                        <li>日语本土化优化</li>
                        <li>端到端现代化服务</li>
                        <li>2026年建立500人专业团队</li>
                    </ul>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 85%"></div>
                    </div>
                    <small>综合竞争力：85%</small>
                </div>

                <div class="comparison-card">
                    <div class="company-header hitachi-header">
                        <i class="fas fa-industry"></i> 日立 (Hitachi)
                    </div>
                    <div style="margin: 15px 0;">
                        <span class="tag">技术实力</span>
                        <span class="tag">18.1%份额</span>
                        <span class="tag">系列关系</span>
                    </div>
                    <h4>竞争优势</h4>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>大型机系统专业知识</li>
                        <li>长期客户关系基础</li>
                        <li>本土化服务体系</li>
                        <li>政府企业深度合作</li>
                        <li>IT基础设施集成优势</li>
                    </ul>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 75%"></div>
                    </div>
                    <small>综合竞争力：75%</small>
                </div>

                <div class="comparison-card">
                    <div class="company-header nec-header">
                        <i class="fas fa-microchip"></i> NEC
                    </div>
                    <div style="margin: 15px 0;">
                        <span class="tag">ACOS系统</span>
                        <span class="tag">16.9%份额</span>
                        <span class="tag">传统强者</span>
                    </div>
                    <h4>核心优势</h4>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>ACOS系列大型机</li>
                        <li>金融行业深度合作</li>
                        <li>本地技术支持</li>
                        <li>系统集成经验丰富</li>
                        <li>面向云转型布局</li>
                    </ul>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 70%"></div>
                    </div>
                    <small>综合竞争力：70%</small>
                </div>

                <div class="comparison-card">
                    <div class="company-header ibm-header">
                        <i class="fas fa-robot"></i> IBM Watsonx
                    </div>
                    <div style="margin: 15px 0;">
                        <span class="tag">AI技术领先</span>
                        <span class="tag">全球经验</span>
                        <span class="tag">创新驱动</span>
                    </div>
                    <h4>差异化优势</h4>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>生成式AI技术领先</li>
                        <li>专用LLM模型</li>
                        <li>混合云部署灵活</li>
                        <li>全球最佳实践</li>
                        <li>持续创新投入</li>
                    </ul>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 80%"></div>
                    </div>
                    <small>技术竞争力：80%</small>
                </div>
            </div>

            <h3><i class="fas fa-balance-scale"></i> 竞争态势分析</h3>
            <div class="chart-container">
                <canvas id="competitiveChart"></canvas>
            </div>
        </div>

        <!-- 5. 市场机会与挑战 -->
        <div id="opportunities" class="section">
            <div class="section-header">
                <h2><i class="fas fa-lightbulb icon"></i>市场机会与挑战分析</h2>
            </div>

            <div class="swot-grid">
                <div class="swot-item strengths">
                    <h3><i class="fas fa-thumbs-up"></i> 优势 (Strengths)</h3>
                    <ul style="margin: 15px 0; padding-left: 20px;">
                        <li>领先的生成式AI技术</li>
                        <li>专门针对大型机的LLM模型</li>
                        <li>全球COBOL现代化经验</li>
                        <li>IBM品牌认知度和信任度</li>
                        <li>混合云部署灵活性</li>
                        <li>持续的研发投入和创新</li>
                    </ul>
                </div>

                <div class="swot-item weaknesses">
                    <h3><i class="fas fa-thumbs-down"></i> 劣势 (Weaknesses)</h3>
                    <ul style="margin: 15px 0; padding-left: 20px;">
                        <li>日本市场份额相对较低</li>
                        <li>本土化程度不如竞争对手</li>
                        <li>缺乏深度的日语技术支持</li>
                        <li>客户关系网络有限</li>
                        <li>系列(Keiretsu)关系缺失</li>
                        <li>面对面服务文化适应不足</li>
                    </ul>
                </div>

                <div class="swot-item opportunities">
                    <h3><i class="fas fa-lightbulb"></i> 机会 (Opportunities)</h3>
                    <ul style="margin: 15px 0; padding-left: 20px;">
                        <li>COBOL程序员短缺加剧</li>
                        <li>数字化转型政策推动</li>
                        <li>年轻开发者偏好新技术</li>
                        <li>监管合规压力增加</li>
                        <li>云迁移趋势加速</li>
                        <li>AI技术接受度提升</li>
                    </ul>
                </div>

                <div class="swot-item threats">
                    <h3><i class="fas fa-exclamation-triangle"></i> 威胁 (Threats)</h3>
                    <ul style="margin: 15px 0; padding-left: 20px;">
                        <li>本土厂商的既得利益优势</li>
                        <li>保守的企业文化和决策</li>
                        <li>高转换成本和风险规避</li>
                        <li>技术民族主义倾向</li>
                        <li>富士通等本土竞争对手的AI追赶</li>
                        <li>经济不确定性影响IT投资</li>
                    </ul>
                </div>
            </div>

            <div class="alert alert-success">
                <i class="fas fa-star"></i>
                <strong>关键机会窗口：</strong>随着COBOL程序员短缺问题日益严重，以及新一代技术决策者对AI技术的开放态度，
                IBM有机会通过差异化的AI技术优势打开市场突破口。
            </div>
        </div>

        <!-- 6. 渠道策略分析 -->
        <div id="channel-strategy" class="section">
            <div class="section-header">
                <h2><i class="fas fa-network-wired icon"></i>销售渠道策略分析</h2>
            </div>

            <h3><i class="fas fa-map-marked"></i> 日本市场渠道特点</h3>
            <div class="comparison-grid">
                <div>
                    <h4><i class="fas fa-handshake"></i> 关系导向文化</h4>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>重视长期合作关系</li>
                        <li>面对面商务交流偏好</li>
                        <li>通过可信任第三方介绍</li>
                        <li>忠诚度和义务感文化</li>
                    </ul>
                </div>
                <div>
                    <h4><i class="fas fa-building"></i> 系列(Keiretsu)网络</h4>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>大型综合商业集团体系</li>
                        <li>银行和贸易公司为中心</li>
                        <li>供应链深度整合</li>
                        <li>排他性合作关系</li>
                    </ul>
                </div>
            </div>

            <h3><i class="fas fa-route"></i> 推荐渠道策略</h3>
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <h4>阶段一：合作伙伴建立</h4>
                        <p>与本土系统集成商、咨询公司建立战略合作关系，利用其客户网络和市场知识</p>
                        <div style="margin: 10px 0;">
                            <span class="tag">本土SI合作</span>
                            <span class="tag">技术认证</span>
                            <span class="tag">联合方案</span>
                        </div>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <h4>阶段二：生态系统构建</h4>
                        <p>建立包括技术厂商、服务提供商、培训机构的完整生态系统</p>
                        <div style="margin: 10px 0;">
                            <span class="tag">生态合作</span>
                            <span class="tag">人才培养</span>
                            <span class="tag">标准制定</span>
                        </div>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <h4>阶段三：直接客户开发</h4>
                        <p>基于成功案例和品牌认知，逐步建立直接客户关系</p>
                        <div style="margin: 10px 0;">
                            <span class="tag">标杆客户</span>
                            <span class="tag">直销团队</span>
                            <span class="tag">技术支持</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="alert alert-info">
                <i class="fas fa-lightbulb"></i>
                <strong>渠道建议：</strong>重点建立与JETRO、主要商会、行业协会的合作关系，
                通过权威第三方介绍进入目标客户，同时加强IBM日本分公司的本地化服务能力。
            </div>
        </div>

        <!-- 7. 定价策略分析 -->
        <div id="pricing-analysis" class="section">
            <div class="section-header">
                <h2><i class="fas fa-yen-sign icon"></i>定价策略深度分析</h2>
            </div>

            <h3><i class="fas fa-table"></i> IBM Watsonx定价结构</h3>
            <table class="pricing-table">
                <thead>
                    <tr>
                        <th>计划类型</th>
                        <th>价格</th>
                        <th>适用对象</th>
                        <th>核心特性</th>
                        <th>竞争优势</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Individual</strong></td>
                        <td>免费</td>
                        <td>个人开发者</td>
                        <td>本地笔记本运行</td>
                        <td>零门槛试用</td>
                    </tr>
                    <tr>
                        <td><strong>Essentials</strong></td>
                        <td>~$2/20任务提示</td>
                        <td>小型项目</td>
                        <td>按需付费模式</td>
                        <td>灵活计费</td>
                    </tr>
                    <tr>
                        <td><strong>Standard</strong> ⭐</td>
                        <td>$3000+/月</td>
                        <td>企业用户</td>
                        <td>无限用户+高级功能</td>
                        <td>性价比优势</td>
                    </tr>
                    <tr>
                        <td><strong>On-premises</strong></td>
                        <td>定制报价</td>
                        <td>大型企业</td>
                        <td>本地部署+定制化</td>
                        <td>安全合规</td>
                    </tr>
                </tbody>
            </table>

            <div class="highlight-box">
                <i class="fas fa-calculator"></i>
                <strong>ROI价值主张：</strong>基于35%生产力提升，月投资$750可实现年节约$3156，投资回报周期约3个月
            </div>

            <h3><i class="fas fa-chart-line"></i> 定价策略分析</h3>
            <div class="comparison-grid">
                <div>
                    <h4><i class="fas fa-plus-circle"></i> 定价优势</h4>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li><strong>按需付费：</strong>避免大额前期投资</li>
                        <li><strong>分层定价：</strong>满足不同规模客户需求</li>
                        <li><strong>促销激励：</strong>75折优惠降低尝试门槛</li>
                        <li><strong>免费试用：</strong>30天试用期降低决策风险</li>
                        <li><strong>ROI导向：</strong>基于生产力提升的价值定价</li>
                    </ul>
                </div>
                <div>
                    <h4><i class="fas fa-exclamation-triangle"></i> 定价挑战</h4>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li><strong>成本敏感：</strong>日本企业对IT成本控制严格</li>
                        <li><strong>预算周期：</strong>年度预算制影响采购决策</li>
                        <li><strong>竞争压力：</strong>本土厂商可能采用价格战</li>
                        <li><strong>汇率风险：</strong>美元计价增加成本不确定性</li>
                        <li><strong>隐性成本：</strong>培训、集成成本需要考虑</li>
                    </ul>
                </div>
            </div>

            <div class="chart-container">
                <canvas id="pricingChart"></canvas>
            </div>

            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>定价建议：</strong>考虑推出日本市场专用定价方案，包括日元计价、年度合同折扣、
                以及针对金融行业的合规认证费用包等，提高价格竞争力和本土化水平。
            </div>
        </div>

        <!-- 8. 技术实现对比 -->
        <div id="technical-comparison" class="section">
            <div class="section-header">
                <h2><i class="fas fa-code icon"></i>技术实现能力对比分析</h2>
            </div>

            <h3><i class="fas fa-robot"></i> AI技术对比</h3>
            <div class="comparison-grid">
                <div class="comparison-card">
                    <h4 style="color: #1e3c72;"><i class="fas fa-brain"></i> IBM Watsonx Code Assistant</h4>
                    <div style="margin: 15px 0;">
                        <span class="tag" style="background: #1e3c72;">生成式AI</span>
                        <span class="tag" style="background: #2a5298;">专用LLM</span>
                        <span class="tag" style="background: #667eea;">智能理解</span>
                    </div>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>基于大型机专用微调的LLM</li>
                        <li>智能代码理解和转换</li>
                        <li>自动化依赖关系分析</li>
                        <li>增量现代化支持</li>
                        <li>动态重构能力</li>
                    </ul>
                </div>

                <div class="comparison-card">
                    <h4 style="color: #ff6b6b;"><i class="fas fa-cogs"></i> 富士通 PROGRESSION</h4>
                    <div style="margin: 15px 0;">
                        <span class="tag" style="background: #ff6b6b;">传统转换</span>
                        <span class="tag" style="background: #ee5a24;">成熟工具</span>
                        <span class="tag" style="background: #fd79a8;">本土化</span>
                    </div>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>基于规则的代码转换</li>
                        <li>50+项目经验积累</li>
                        <li>日语环境深度优化</li>
                        <li>端到端服务支持</li>
                        <li>GS21系列兼容性</li>
                    </ul>
                </div>
            </div>

            <h3><i class="fas fa-chart-radar"></i> 技术能力雷达图</h3>
            <div class="chart-container">
                <canvas id="techRadarChart"></canvas>
            </div>

            <h3><i class="fas fa-lightbulb"></i> AI智能体平台借鉴</h3>
            <p style="margin: 15px 0; font-size: 1.1em;">
                基于用户提供的AI智能体平台方案文档分析，IBM可以借鉴以下技术实现思路：
            </p>

            <div class="comparison-grid">
                <div>
                    <h4><i class="fas fa-users-cog"></i> 多Agent协作模式</h4>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>标准化确认Agent：文档标准化处理</li>
                        <li>任务拆分Agent：复杂任务智能拆解</li>
                        <li>规约Agent：代码规范检查</li>
                        <li>生成Agent：代码自动生成</li>
                        <li>复检Agent：质量保证验证</li>
                        <li>修正Agent：问题自动修复</li>
                    </ul>
                </div>
                <div>
                    <h4><i class="fas fa-database"></i> 知识库标准化</h4>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>项目知识库：业务术语和流程</li>
                        <li>技术规约库：代码标准和规范</li>
                        <li>模板库：常用代码模式</li>
                        <li>问题库：历史问题解决方案</li>
                        <li>决策记录：技术选型依据</li>
                        <li>最佳实践：成功案例积累</li>
                    </ul>
                </div>
            </div>

            <div class="alert alert-success">
                <i class="fas fa-rocket"></i>
                <strong>技术优势总结：</strong>IBM的生成式AI技术相比传统转换工具具有明显的智能化优势，
                但需要加强日语环境的本土化适配和知识库建设，提升在日本市场的技术竞争力。
            </div>
        </div>

        <!-- 9. 服务体系分析 -->
        <div id="service-analysis" class="section">
            <div class="section-header">
                <h2><i class="fas fa-hands-helping icon"></i>服务体系竞争分析</h2>
            </div>

            <h3><i class="fas fa-trophy"></i> 本土厂商服务优势</h3>
            <div class="comparison-grid">
                <div>
                    <h4><i class="fas fa-globe-asia"></i> 富士通服务体系</h4>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li><strong>专业团队：</strong>2026年建立500人专业组织</li>
                        <li><strong>工程组织：</strong>2024年建设2000人工程团队</li>
                        <li><strong>全球经验：</strong>50+客户现代化经验</li>
                        <li><strong>本土化：</strong>深度日语技术支持</li>
                        <li><strong>端到端：</strong>从规划到实施的全流程服务</li>
                    </ul>
                </div>
                <div>
                    <h4><i class="fas fa-building"></i> 日立/NEC服务特点</h4>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li><strong>长期关系：</strong>数十年客户合作基础</li>
                        <li><strong>系统集成：</strong>深度IT基础设施整合</li>
                        <li><strong>面对面服务：</strong>本地化客户服务文化</li>
                        <li><strong>政企合作：</strong>政府和大企业关系网络</li>
                        <li><strong>风险共担：</strong>项目风险共同承担模式</li>
                    </ul>
                </div>
            </div>

            <h3><i class="fas fa-chart-bar"></i> IBM服务能力缺口分析</h3>
            <div class="metrics-grid">
                <div class="metric-card" style="background: linear-gradient(45deg, #ff4757 0%, #ff3838 100%);">
                    <div class="metric-value">30%</div>
                    <div>本土化程度</div>
                    <small>相比本土厂商的本土化水平</small>
                </div>
                <div class="metric-card" style="background: linear-gradient(45deg, #ffa502 0%, #ff6348 100%);">
                    <div class="metric-value">40%</div>
                    <div>日语技术支持</div>
                    <small>日语技术文档和支持覆盖率</small>
                </div>
                <div class="metric-card" style="background: linear-gradient(45deg, #3742fa 0%, #2f3542 100%);">
                    <div class="metric-value">80%</div>
                    <div>技术能力</div>
                    <small>AI技术领先优势</small>
                </div>
                <div class="metric-card" style="background: linear-gradient(45deg, #20bf6b 0%, #01a3a4 100%);">
                    <div class="metric-value">70%</div>
                    <div>全球经验</div>
                    <small>国际化项目经验优势</small>
                </div>
            </div>

            <h3><i class="fas fa-route"></i> 服务体系提升建议</h3>
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <h4>本土化服务增强</h4>
                        <p>建立日本专属的技术支持团队，提供7x24日语技术服务，开发日语版本的技术文档和培训材料</p>
                        <div style="margin: 10px 0;">
                            <span class="tag">日语支持</span>
                            <span class="tag">本地团队</span>
                            <span class="tag">文化适应</span>
                        </div>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <h4>合作伙伴生态建设</h4>
                        <p>与本土系统集成商建立深度合作关系，通过合作伙伴提供本地化的实施和运维服务</p>
                        <div style="margin: 10px 0;">
                            <span class="tag">SI合作</span>
                            <span class="tag">生态共建</span>
                            <span class="tag">能力互补</span>
                        </div>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <h4>专业服务能力提升</h4>
                        <p>建立COBOL现代化CoE（卓越中心），提供从评估、迁移到运维的端到端专业服务</p>
                        <div style="margin: 10px 0;">
                            <span class="tag">专业CoE</span>
                            <span class="tag">端到端服务</span>
                            <span class="tag">最佳实践</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 10. 建议和结论 -->
        <div id="recommendations" class="section">
            <div class="section-header">
                <h2><i class="fas fa-star icon"></i>战略建议与结论</h2>
            </div>

            <div class="recommendation-box">
                <h3><i class="fas fa-bullseye"></i> 核心竞争策略建议</h3>
                <p style="font-size: 1.1em; margin: 15px 0;">
                    基于深度市场分析，IBM应采用"AI技术领先+生态合作+本土化服务"的差异化竞争策略，
                通过技术创新优势打破本土厂商的既有垄断，实现市场份额的逐步提升。
                </p>
            </div>

            <h3><i class="fas fa-tasks"></i> 五大关键行动计划</h3>
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <h4>1. 技术差异化突破</h4>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li>强化生成式AI技术的日语环境适配</li>
                            <li>开发针对日本金融行业的专用模型</li>
                            <li>建立AI智能体多Agent协作平台</li>
                            <li>提供传统转换工具无法实现的智能化能力</li>
                        </ul>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <h4>2. 生态合作伙伴战略</h4>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li>与主要日本SI厂商建立战略合作关系</li>
                            <li>通过JETRO等官方渠道获得权威认可</li>
                            <li>联合制定COBOL现代化行业标准</li>
                            <li>建立技术培训和认证体系</li>
                        </ul>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <h4>3. 本土化服务加强</h4>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li>建立日本专属的技术支持中心</li>
                            <li>招募本土COBOL和AI技术专家</li>
                            <li>提供日语版本的全套技术文档</li>
                            <li>适应日本企业的项目管理文化</li>
                        </ul>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <h4>4. 灵活定价策略</h4>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li>推出日本市场专用定价方案</li>
                            <li>提供日元计价和年度合同优惠</li>
                            <li>针对金融行业推出合规认证包</li>
                            <li>建立基于ROI的价值定价模式</li>
                        </ul>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <h4>5. 渐进式市场渗透</h4>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li>从具有国际业务的日本企业开始</li>
                            <li>建立标杆客户和成功案例</li>
                            <li>逐步向传统日本企业市场扩展</li>
                            <li>利用口碑效应和客户推荐</li>
                        </ul>
                    </div>
                </div>
            </div>

            <h3><i class="fas fa-chart-line"></i> 预期市场表现</h3>
            <div class="metrics-grid">
                <div class="metric-card" style="background: linear-gradient(45deg, #4CAF50 0%, #8BC34A 100%);">
                    <div class="metric-value">15%</div>
                    <div>3年目标市场份额</div>
                    <small>从当前低基数实现突破性增长</small>
                </div>
                <div class="metric-card" style="background: linear-gradient(45deg, #2196F3 0%, #03A9F4 100%);">
                    <div class="metric-value">50+</div>
                    <div>目标客户数量</div>
                    <small>重点关注大中型金融机构</small>
                </div>
                <div class="metric-card" style="background: linear-gradient(45deg, #FF9800 0%, #F57C00 100%);">
                    <div class="metric-value">¥5000万</div>
                    <div>年收入目标</div>
                    <small>基于标准计划价格估算</small>
                </div>
                <div class="metric-card" style="background: linear-gradient(45deg, #9C27B0 0%, #E91E63 100%);">
                    <div class="metric-value">18个月</div>
                    <div>盈亏平衡周期</div>
                    <small>考虑本土化投资成本</small>
                </div>
            </div>

            <div class="highlight-box">
                <i class="fas fa-flag-checkered"></i>
                <strong>结论：</strong>
                尽管日本COBOL现代化市场被本土厂商主导，但IBM通过差异化的AI技术优势、
                灵活的合作策略和持续的本土化投入，有望在这个高价值市场中获得重要突破，
                实现技术领先性与商业成功的双重目标。
            </div>
        </div>

        <!-- 页脚 -->
        <div class="footer">
            <p><i class="fas fa-chart-line"></i> 本报告基于公开信息和市场调研数据分析生成</p>
            <p>© 2025 AI市场研究团队 | 报告生成时间：2025年6月6日</p>
        </div>
    </div>

    <!-- JavaScript for Charts -->
    <script>
        // 产品功能特性雷达图
        const ctx1 = document.getElementById('productFeatureChart').getContext('2d');
        new Chart(ctx1, {
            type: 'radar',
            data: {
                labels: ['AI领先性', 'COBOL专长', '现代化能力', '部署灵活性', '成本效益', '用户体验'],
                datasets: [{
                    label: 'Watsonx Code Assistant',
                    data: [95, 90, 85, 80, 75, 80],
                    backgroundColor: 'rgba(102, 126, 234, 0.2)',
                    borderColor: 'rgba(102, 126, 234, 1)',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });

        // 市场份额饼图
        const marketShareChart = echarts.init(document.getElementById('marketShareChart'));
        const marketShareOption = {
            title: {
                text: '日本大型机市场份额分布',
                left: 'center'
            },
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b} : {c}% ({d}%)'
            },
            legend: {
                orient: 'vertical',
                left: 'left'
            },
            series: [
                {
                    name: '市场份额',
                    type: 'pie',
                    radius: '50%',
                    data: [
                        {value: 24.5, name: '富士通', itemStyle: {color: '#ff6b6b'}},
                        {value: 18.1, name: '日立', itemStyle: {color: '#20bf6b'}},
                        {value: 16.9, name: 'NEC', itemStyle: {color: '#8854d0'}},
                        {value: 15.0, name: 'IBM', itemStyle: {color: '#1e3c72'}},
                        {value: 25.5, name: '其他厂商', itemStyle: {color: '#95a5a6'}}
                    ],
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }
            ]
        };
        marketShareChart.setOption(marketShareOption);

        // 竞争力对比图
        const ctx2 = document.getElementById('competitiveChart').getContext('2d');
        new Chart(ctx2, {
            type: 'bar',
            data: {
                labels: ['技术创新', '市场份额', '客户关系', '本土化', '服务能力', '品牌认知'],
                datasets: [
                    {
                        label: 'IBM Watsonx',
                        data: [95, 40, 50, 30, 60, 80],
                        backgroundColor: 'rgba(30, 60, 114, 0.8)'
                    },
                    {
                        label: '富士通',
                        data: [60, 85, 90, 95, 85, 85],
                        backgroundColor: 'rgba(255, 107, 107, 0.8)'
                    },
                    {
                        label: '日立',
                        data: [65, 75, 85, 90, 80, 80],
                        backgroundColor: 'rgba(32, 191, 107, 0.8)'
                    },
                    {
                        label: 'NEC',
                        data: [60, 70, 80, 85, 75, 75],
                        backgroundColor: 'rgba(136, 84, 208, 0.8)'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });

        // 定价对比图
        const ctx3 = document.getElementById('pricingChart').getContext('2d');
        new Chart(ctx3, {
            type: 'line',
            data: {
                labels: ['个人版', '基础版', '标准版', '企业版', '本地部署'],
                datasets: [
                    {
                        label: 'IBM Watsonx (USD)',
                        data: [0, 100, 3000, 8000, 15000],
                        borderColor: 'rgba(30, 60, 114, 1)',
                        backgroundColor: 'rgba(30, 60, 114, 0.2)',
                        tension: 0.4
                    },
                    {
                        label: '竞争对手预估 (USD)',
                        data: [0, 80, 2500, 7000, 12000],
                        borderColor: 'rgba(255, 107, 107, 1)',
                        backgroundColor: 'rgba(255, 107, 107, 0.2)',
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // 技术能力雷达图
        const ctx4 = document.getElementById('techRadarChart').getContext('2d');
        new Chart(ctx4, {
            type: 'radar',
            data: {
                labels: ['AI技术', 'COBOL理解', '代码转换', '本土化', '用户体验', '集成能力'],
                datasets: [
                    {
                        label: 'IBM Watsonx',
                        data: [95, 90, 88, 40, 80, 75],
                        backgroundColor: 'rgba(30, 60, 114, 0.2)',
                        borderColor: 'rgba(30, 60, 114, 1)',
                        borderWidth: 2
                    },
                    {
                        label: '富士通 PROGRESSION',
                        data: [60, 85, 90, 95, 85, 90],
                        backgroundColor: 'rgba(255, 107, 107, 0.2)',
                        borderColor: 'rgba(255, 107, 107, 1)',
                        borderWidth: 2
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });

        // 响应式图表大小调整
        window.addEventListener('resize', function() {
            marketShareChart.resize();
        });

        // 平滑滚动到锚点
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 进度条动画
        function animateProgressBars() {
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 500);
            });
        }

        // 页面加载完成后执行动画
        window.addEventListener('load', () => {
            setTimeout(animateProgressBars, 1000);
        });
    </script>

      <script>
        const createFloatingBall = ()=>{let a=document.createElement("style");a.textContent=`
          .fellou-floating-ball {
              position: fixed;
              bottom: 20px;
              right: 20px;
              background: #fff;
              border-radius: 50%;
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
              display: flex;
              gap: 8px;
              flex-direction: row;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              transition: all 0.15s ease;
              z-index: 100000;
              width: 28px;
              height: 28px;
              text-align: center;
              border: 2px solid #f4f4f4;
          }

          .fellou-floating-ball:hover {
              width: 140px;
              border-radius: 99px;
          }

          .fellou-floating-ball svg {
              width: 16px;
              height: 16px;
          }

          .fellou-floating-ball-text {
              display: none;
              width: 0px;
              transition: width 0.3s ease;
              color: #595561;
              font-family: Roboto;
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 20px;
              white-space: nowrap;
              overflow: hidden;
           
          }

          .fellou-floating-ball:hover .fellou-floating-ball-text {
              display: block;
              width: 100px;
          }
      `,document.head.appendChild(a);let b=document.createElement("div");b.className="fellou-floating-ball",b.addEventListener("click",()=>{window.open("https://fellou.ai","_blank")}),b.innerHTML=`
          <svg width="16" height="16" viewBox="0 0 152 152" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path opacity="0.72" d="M108.71 15.909C133.671 36.8541 134.337 77.1554 110.197 105.924C86.0565 134.694 46.2518 141.036 21.2904 120.091C-3.67096 99.1459 -4.33674 58.8446 19.8034 30.0755C43.9435 1.30644 83.7482 -5.03614 108.71 15.909ZM102.282 23.5694C81.8118 6.39315 48.2407 11.7425 27.4638 36.5034C6.68694 61.2643 7.24845 95.2543 27.7183 112.431C48.1882 129.607 81.7593 124.258 102.536 99.4966C123.313 74.7357 122.752 40.7457 102.282 23.5694Z" fill="url(#paint0_linear_34_1408)"/>
              <path d="M116.986 29.3811C141.525 49.9712 143.286 88.2698 120.921 114.924C98.5561 141.577 60.5333 146.493 35.995 125.903C11.4567 105.313 9.69493 67.0139 32.06 40.3602C54.4252 13.7065 92.4479 8.79095 116.986 29.3811ZM110.558 37.0415C90.3987 20.1255 58.6488 24.2301 39.7205 46.788C20.7921 69.346 22.2632 101.326 42.4229 118.242C62.5825 135.158 94.3324 131.054 113.261 108.496C132.189 85.9377 130.718 53.9574 110.558 37.0415Z" fill="url(#paint1_linear_34_1408)"/>
              <path d="M131.544 35.0694C155.71 55.3471 155.731 95.1074 131.591 123.876C107.451 152.646 68.291 159.529 44.1249 139.251C19.9589 118.974 19.9379 79.2135 44.078 50.4444C68.2182 21.6753 107.378 14.7917 131.544 35.0694ZM125.116 42.7299C105.505 26.2745 72.5526 32.067 51.7385 56.8723C30.9244 81.6776 30.9421 115.136 50.5528 131.591C70.1636 148.046 103.116 142.254 123.931 117.449C144.745 92.6433 144.727 59.1852 125.116 42.7299Z" fill="url(#paint2_linear_34_1408)"/>
              <defs>
                  <linearGradient id="paint0_linear_34_1408" x1="108.71" y1="15.909" x2="21.2904" y2="120.091" gradientUnits="userSpaceOnUse">
                      <stop stop-color="#6401F8" stop-opacity="0.7"/>
                      <stop offset="0.465" stop-color="#FF9000" stop-opacity="0.42"/>
                      <stop offset="1" stop-color="#33B3FF" stop-opacity="0.2"/>
                  </linearGradient>
                  <linearGradient id="paint1_linear_34_1408" x1="116.986" y1="29.381" x2="35.995" y2="125.903" gradientUnits="userSpaceOnUse">
                      <stop stop-color="#6401F8" stop-opacity="0.7"/>
                      <stop offset="0.465" stop-color="#FF9000" stop-opacity="0.42"/>
                      <stop offset="1" stop-color="#33B3FF" stop-opacity="0.2"/>
                  </linearGradient>
                  <linearGradient id="paint2_linear_34_1408" x1="131.544" y1="35.0694" x2="44.1249" y2="139.251" gradientUnits="userSpaceOnUse">
                      <stop stop-color="#6401F8"/>
                      <stop offset="0.5" stop-color="#FF9000"/>
                      <stop offset="1" stop-color="#33B3FF"/>
                  </linearGradient>
              </defs>
          </svg>
      `;let c=document.createElement("div");c.className="fellou-floating-ball-text",c.textContent="powered by fellou",b.appendChild(c),document.body.appendChild(b)};
        // Call createFloatingBall when the page loads
        window.addEventListener('load', createFloatingBall);
      </script>
    </body>
</html>