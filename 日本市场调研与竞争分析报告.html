<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>超自动化软件开发平台 - 日本市场分析报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .report-header {
            background: white;
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .report-header h1 {
            color: #2c3e50;
            font-size: 2.8em;
            font-weight: 700;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .report-header .subtitle {
            font-size: 1.3em;
            color: #7f8c8d;
            margin-bottom: 20px;
        }

        .executive-summary {
            background: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .section {
            background: white;
            margin-bottom: 30px;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .section:hover {
            transform: translateY(-5px);
        }

        .section-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px 30px;
            font-size: 1.5em;
            font-weight: 600;
        }

        .section-content {
            padding: 30px;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .metric-card {
            background: linear-gradient(135deg, #a8edea, #fed6e3);
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .metric-value {
            font-size: 2.2em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .metric-label {
            font-size: 1.1em;
            color: #34495e;
            font-weight: 600;
        }

        .chart-container {
            height: 400px;
            margin: 25px 0;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }

        .competitive-matrix {
            overflow-x: auto;
            margin: 25px 0;
        }

        .matrix-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .matrix-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: 600;
        }

        .matrix-table td {
            padding: 12px 15px;
            text-align: center;
            border-bottom: 1px solid #ecf0f1;
        }

        .matrix-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .score-high { background-color: #2ecc71; color: white; border-radius: 20px; padding: 5px 10px; }
        .score-medium { background-color: #f39c12; color: white; border-radius: 20px; padding: 5px 10px; }
        .score-low { background-color: #e74c3c; color: white; border-radius: 20px; padding: 5px 10px; }

        .swot-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 25px 0;
        }

        .swot-item {
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .strengths { background: linear-gradient(135deg, #a8edea, #fed6e3); }
        .weaknesses { background: linear-gradient(135deg, #ffecd2, #fcb69f); }
        .opportunities { background: linear-gradient(135deg, #a8e6cf, #dcedc1); }
        .threats { background: linear-gradient(135deg, #ffd3a5, #fd9853); }

        .swot-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .strategy-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .strategy-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-left: 5px solid #667eea;
            transition: all 0.3s ease;
        }

        .strategy-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .strategy-title {
            font-size: 1.4em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .priority-high { border-left-color: #e74c3c; }
        .priority-medium { border-left-color: #f39c12; }
        .priority-low { border-left-color: #2ecc71; }

        ul, ol {
            padding-left: 25px;
            margin: 15px 0;
        }

        li {
            margin: 8px 0;
            line-height: 1.6;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .report-header h1 {
                font-size: 2.2em;
            }
            
            .swot-grid {
                grid-template-columns: 1fr;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
        }

        .footer {
            background: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-top: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 报告标题 -->
        <div class="report-header">
            <h1>超自动化软件开发平台</h1>
            <div class="subtitle">日本市场调研与竞争分析报告</div>
            <div style="color: #95a5a6; margin-top: 15px;">
                <strong>聚焦：</strong>金融行业 • Cobol转Java • 对日开发
            </div>
            <div style="color: #95a5a6; margin-top: 10px; font-size: 1em;">
                报告生成时间：2025年6月5日
            </div>
        </div>

        <!-- 执行摘要 -->
        <div class="executive-summary">
            <h2 style="color: #2c3e50; margin-bottom: 20px; font-size: 1.8em;">📋 执行摘要</h2>
            <div class="highlight">
                基于详细的市场调研和竞争分析，超自动化软件开发平台在日本市场，特别是金融行业的Cobol转换项目领域，面临着<strong>巨大的市场机遇</strong>。日本政府"2025年悬崖"政策压力、金融机构数字化转型需求，以及Cobol人才短缺问题，为我们的AI驱动自动化开发平台提供了<strong>完美的市场时机窗口</strong>。
            </div>

            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">¥337亿</div>
                    <div class="metric-label">2033年日本软件市场规模</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">4%</div>
                    <div class="metric-label">年复合增长率(CAGR)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">¥1,900亿</div>
                    <div class="metric-label">大银行年均IT投资</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">30-50%</div>
                    <div class="metric-label">开发效率提升潜力</div>
                </div>
            </div>
        </div>

        <!-- 市场机会分析 -->
        <div class="section">
            <div class="section-header">
                🎯 市场机会分析
            </div>
            <div class="section-content">
                <div class="chart-container">
                    <canvas id="marketOpportunityChart"></canvas>
                </div>

                <h3>核心市场驱动因素</h3>
                <ol>
                    <li><strong>政策压力</strong>：经济产业省"2025年悬崖"警告，预计最大12兆日元经济损失风险</li>
                    <li><strong>技术债务</strong>：Cobol系统维护人才退休，主机制造商2035年停产</li>
                    <li><strong>监管要求</strong>：金融厅强化IT系统现代化和网络安全要求</li>
                    <li><strong>数字化转型</strong>：三大银行集团积极推进DX，AI投资优先</li>
                </ol>

                <h3>目标客户规模</h3>
                <ul>
                    <li><strong>一级目标</strong>：3大Megabank（年IT预算1,500-2,000亿日元）</li>
                    <li><strong>二级目标</strong>：地方银行（年IT预算50-500亿日元）</li>
                    <li><strong>三级目标</strong>：信用金库・協同組合（年IT预算5-50亿日元）</li>
                </ul>
            </div>
        </div>

        <!-- 竞争态势分析 -->
        <div class="section">
            <div class="section-header">
                ⚔️ 竞争态势分析
            </div>
            <div class="section-content">
                <div class="chart-container">
                    <div id="competitorChart" style="width: 100%; height: 400px;"></div>
                </div>

                <h3>主要竞争对手矩阵</h3>
                <div class="competitive-matrix">
                    <table class="matrix-table">
                        <thead>
                            <tr>
                                <th>竞争对手</th>
                                <th>技术能力</th>
                                <th>市场份额</th>
                                <th>Cobol专精</th>
                                <th>AI能力</th>
                                <th>本土化</th>
                                <th>综合评分</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>超自动化平台</strong></td>
                                <td><span class="score-high">9.5</span></td>
                                <td><span class="score-low">2.0</span></td>
                                <td><span class="score-high">9.0</span></td>
                                <td><span class="score-high">9.5</span></td>
                                <td><span class="score-medium">7.0</span></td>
                                <td><span class="score-high">7.4</span></td>
                            </tr>
                            <tr>
                                <td>IBM Watson</td>
                                <td><span class="score-high">9.0</span></td>
                                <td><span class="score-high">8.5</span></td>
                                <td><span class="score-medium">7.0</span></td>
                                <td><span class="score-high">8.5</span></td>
                                <td><span class="score-medium">6.5</span></td>
                                <td><span class="score-high">7.9</span></td>
                            </tr>
                            <tr>
                                <td>富士通 PROGRESSION</td>
                                <td><span class="score-medium">7.5</span></td>
                                <td><span class="score-medium">7.0</span></td>
                                <td><span class="score-high">9.0</span></td>
                                <td><span class="score-medium">6.5</span></td>
                                <td><span class="score-high">9.5</span></td>
                                <td><span class="score-medium">7.9</span></td>
                            </tr>
                            <tr>
                                <td>Microsoft Power Platform</td>
                                <td><span class="score-medium">7.0</span></td>
                                <td><span class="score-high">9.0</span></td>
                                <td><span class="score-low">3.0</span></td>
                                <td><span class="score-medium">7.5</span></td>
                                <td><span class="score-medium">6.0</span></td>
                                <td><span class="score-medium">6.5</span></td>
                            </tr>
                            <tr>
                                <td>NEC</td>
                                <td><span class="score-medium">7.0</span></td>
                                <td><span class="score-medium">6.5</span></td>
                                <td><span class="score-medium">8.0</span></td>
                                <td><span class="score-medium">6.0</span></td>
                                <td><span class="score-high">9.0</span></td>
                                <td><span class="score-medium">7.3</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="highlight">
                    <strong>竞争优势分析：</strong>我们在AI技术能力和Cobol专精方面具有显著优势，但需要在市场份额和本土化服务方面加强投入。
                </div>
            </div>
        </div>

        <!-- SWOT分析 -->
        <div class="section">
            <div class="section-header">
                📊 SWOT分析
            </div>
            <div class="section-content">
                <div class="swot-grid">
                    <div class="swot-item strengths">
                        <div class="swot-title">💪 优势 (Strengths)</div>
                        <ul>
                            <li>AI驱动的端到端自动化开发能力</li>
                            <li>多Agent智能体协作系统</li>
                            <li>30-50%效率提升的量化成果</li>
                            <li>专门针对Cobol到Java转换优化</li>
                            <li>RAG/GraphRAG知识检索技术</li>
                            <li>对日开发流程深度理解</li>
                        </ul>
                    </div>
                    
                    <div class="swot-item weaknesses">
                        <div class="swot-title">⚠️ 劣势 (Weaknesses)</div>
                        <ul>
                            <li>日本市场品牌认知度低</li>
                            <li>缺乏大型项目实施案例</li>
                            <li>本地化服务体系待建设</li>
                            <li>与现有系统集成经验不足</li>
                            <li>销售渠道和合作伙伴网络缺乏</li>
                        </ul>
                    </div>
                    
                    <div class="swot-item opportunities">
                        <div class="swot-title">🌟 机会 (Opportunities)</div>
                        <ul>
                            <li>"2025年悬崖"政策推动系统现代化</li>
                            <li>Cobol人才短缺加速转换需求</li>
                            <li>三大银行DX投资加速</li>
                            <li>AI技术在金融业接受度提高</li>
                            <li>成本压力驱动自动化需求</li>
                            <li>政府DX投资促进税制支持</li>
                        </ul>
                    </div>
                    
                    <div class="swot-item threats">
                        <div class="swot-title">🚨 威胁 (Threats)</div>
                        <ul>
                            <li>IBM、富士通等大厂商强势竞争</li>
                            <li>金融行业对新技术保守态度</li>
                            <li>监管合规要求复杂</li>
                            <li>长期决策流程延缓项目启动</li>
                            <li>客户对供应商稳定性要求高</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 客户需求分析 -->
        <div class="section">
            <div class="section-header">
                👥 目标客户需求分析
            </div>
            <div class="section-content">
                <div class="chart-container">
                    <canvas id="customerNeedsChart"></canvas>
                </div>

                <h3>核心需求痛点</h3>
                <div class="strategy-cards">
                    <div class="strategy-card priority-high">
                        <div class="strategy-title">🔥 Legacy系统现代化</div>
                        <ul>
                            <li>Cobol人才退休高龄化</li>
                            <li>主机系统停产风险</li>
                            <li>维护成本急剧上升</li>
                            <li>系统复杂化黑箱风险</li>
                        </ul>
                    </div>
                    
                    <div class="strategy-card priority-high">
                        <div class="strategy-title">📋 监管合规压力</div>
                        <ul>
                            <li>2025年悬崖政策要求</li>
                            <li>网络安全强化必要性</li>
                            <li>数据保护法规遵守</li>
                            <li>系统可靠性要求</li>
                        </ul>
                    </div>
                    
                    <div class="strategy-card priority-medium">
                        <div class="strategy-title">🚀 数字化转型需求</div>
                        <ul>
                            <li>客户体验现代化</li>
                            <li>业务流程自动化</li>
                            <li>AI・数据分析能力构建</li>
                            <li>云平台迁移</li>
                        </ul>
                    </div>
                    
                    <div class="strategy-card priority-medium">
                        <div class="strategy-title">💰 成本控制压力</div>
                        <ul>
                            <li>IT投资ROI最大化要求</li>
                            <li>人力成本上升压力</li>
                            <li>运营效率提升迫切性</li>
                            <li>维护费用降低需求</li>
                        </ul>
                    </div>
                </div>

                <h3>采购决策特点</h3>
                <div class="highlight">
                    <strong>稟议制度主导：</strong>多层级审批，重视内部合意形成，决策周期通常6-18个月。<br>
                    <strong>评价标准：</strong>技术能力(30%) + 实施体制(25%) + 价格竞争力(20%) + 支持体制(15%) + 信赖关系(10%)
                </div>
            </div>
        </div>

        <!-- 市场进入策略 -->
        <div class="section">
            <div class="section-header">
                🎯 市场进入策略建议
            </div>
            <div class="section-content">
                <div class="strategy-cards">
                    <div class="strategy-card priority-high">
                        <div class="strategy-title">Phase 1: 市场立足 (6-12个月)</div>
                        <ul>
                            <li><strong>本土化投资</strong>：建立日本现地法人和技术支持团队</li>
                            <li><strong>概念验证</strong>：与地方银行进行小规模POC项目</li>
                            <li><strong>合作伙伴</strong>：与日本系统集成商建立合作关系</li>
                            <li><strong>案例积累</strong>：完成2-3个Cobol转换成功案例</li>
                        </ul>
                    </div>
                    
                    <div class="strategy-card priority-high">
                        <div class="strategy-title">Phase 2: 市场拓展 (12-24个月)</div>
                        <ul>
                            <li><strong>大客户攻略</strong>：锁定三大银行中的1-2家进行深度合作</li>
                            <li><strong>产品本土化</strong>：日语界面、法规遵守、安全认证</li>
                            <li><strong>营销推广</strong>：行业会议、技术研讨会、白皮书发布</li>
                            <li><strong>团队扩充</strong>：招聘日本本土销售和技术人员</li>
                        </ul>
                    </div>
                    
                    <div class="strategy-card priority-medium">
                        <div class="strategy-title">Phase 3: 市场主导 (24-36个月)</div>
                        <ul>
                            <li><strong>规模化订单</strong>：获得大型银行的多年期服务合同</li>
                            <li><strong>生态建设</strong>：构建包含培训、认证、社区的完整生态</li>
                            <li><strong>产品演进</strong>：基于客户反馈持续优化平台功能</li>
                            <li><strong>市场领导力</strong>：在Cobol转Java领域建立技术领导地位</li>
                        </ul>
                    </div>
                </div>

                <h3>关键成功因素</h3>
                <ol>
                    <li><strong>技术差异化</strong>：持续保持AI自动化开发的技术领先优势</li>
                    <li><strong>实施能力</strong>：建立专业的项目实施和客户成功团队</li>
                    <li><strong>本土化程度</strong>：深度理解日本企业文化和业务流程</li>
                    <li><strong>生态合作</strong>：与系统集成商、咨询公司建立战略联盟</li>
                    <li><strong>品牌建设</strong>：在金融行业建立可信赖的技术品牌形象</li>
                </ol>
            </div>
        </div>

        <!-- 投资回报预测 -->
        <div class="section">
            <div class="section-header">
                📈 投资回报预测
            </div>
            <div class="section-content">
                <div class="chart-container">
                    <canvas id="roiChart"></canvas>
                </div>

                <h3>收入预测模型</h3>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">¥15亿</div>
                        <div class="metric-label">第1年收入目标</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">¥80亿</div>
                        <div class="metric-label">第3年收入目标</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">¥200亿</div>
                        <div class="metric-label">第5年收入目标</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">45%</div>
                        <div class="metric-label">预期毛利率</div>
                    </div>
                </div>

                <div class="highlight">
                    <strong>投资建议：</strong>建议在未来3年内投资50-80亿日元用于日本市场开拓，预期第4年开始实现正现金流，第5年达到20%的市场份额。
                </div>
            </div>
        </div>

        <!-- 风险评估与应对 -->
        <div class="section">
            <div class="section-header">
                ⚠️ 风险评估与应对策略
            </div>
            <div class="section-content">
                <div class="strategy-cards">
                    <div class="strategy-card priority-high">
                        <div class="strategy-title">🔴 高风险：技术竞争激烈</div>
                        <strong>风险描述：</strong>IBM、富士通等大厂持续投入R&D<br>
                        <strong>应对策略：</strong>
                        <ul>
                            <li>加大AI和自动化技术研发投入</li>
                            <li>申请核心技术专利保护</li>
                            <li>建立技术壁垒和先发优势</li>
                        </ul>
                    </div>
                    
                    <div class="strategy-card priority-medium">
                        <div class="strategy-title">🟡 中风险：监管合规复杂</div>
                        <strong>风险描述：</strong>金融监管要求严格且变化频繁<br>
                        <strong>应对策略：</strong>
                        <ul>
                            <li>建立专业的合规团队</li>
                            <li>与监管机构保持密切沟通</li>
                            <li>产品设计时优先考虑合规要求</li>
                        </ul>
                    </div>
                    
                    <div class="strategy-card priority-low">
                        <div class="strategy-title">🟢 低风险：汇率波动</div>
                        <strong>风险描述：</strong>日元汇率波动影响投资回报<br>
                        <strong>应对策略：</strong>
                        <ul>
                            <li>采用本币定价和结算</li>
                            <li>建立汇率对冲机制</li>
                            <li>分阶段投资降低风险敞口</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 结论与建议 -->
        <div class="section">
            <div class="section-header">
                🎯 结论与行动建议
            </div>
            <div class="section-content">
                <div class="highlight">
                    <h3>核心结论</h3>
                    <p>基于全面的市场调研和竞争分析，<strong>超自动化软件开发平台在日本金融市场面临历史性机遇</strong>。"2025年悬崖"政策压力、Cobol系统现代化紧迫需求、以及我们在AI自动化开发方面的技术优势，构成了进入日本市场的完美时机窗口。</p>
                </div>

                <h3>立即行动建议</h3>
                <ol>
                    <li><strong>启动市场进入计划</strong>：制定详细的18个月日本市场进入路线图</li>
                    <li><strong>建立本土团队</strong>：招聘日本本土销售、技术和合规人才</li>
                    <li><strong>寻找战略合作伙伴</strong>：与日本系统集成商建立合作关系</li>
                    <li><strong>启动POC项目</strong>：与2-3家地方银行开展概念验证</li>
                    <li><strong>产品本土化</strong>：日语界面、日本法规适配、安全认证</li>
                </ol>

                <h3>成功关键指标(KPI)</h3>
                <ul>
                    <li><strong>12个月内</strong>：完成3个POC项目，签约1家地方银行</li>
                    <li><strong>24个月内</strong>：获得1家大银行试点项目，营收达到15亿日元</li>
                    <li><strong>36个月内</strong>：在Cobol转Java市场份额达到10%，营收80亿日元</li>
                </ul>
            </div>
        </div>

        <!-- 页脚 -->
        <div class="footer">
            <h4>超自动化软件开发平台 - 日本市场战略部门</h4>
            <p>报告编制：市场调研团队 | 联系方式：<EMAIL> | 更新时间：2025年6月5日</p>
            <p style="margin-top: 15px; color: #7f8c8d; font-size: 0.9em;">
                本报告基于公开市场数据和行业研究，仅供内部战略决策参考
            </p>
        </div>
    </div>

    <script>
        // 市场机会图表
        const ctx1 = document.getElementById('marketOpportunityChart').getContext('2d');
        new Chart(ctx1, {
            type: 'doughnut',
            data: {
                labels: ['三大银行', '地方银行', '信用金库', '保险公司', '证券公司'],
                datasets: [{
                    data: [45, 25, 15, 10, 5],
                    backgroundColor: ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    title: {
                        display: true,
                        text: '目标市场分布 (按收入潜力)',
                        font: { size: 16, weight: 'bold' }
                    }
                }
            }
        });

        // 客户需求图表
        const ctx2 = document.getElementById('customerNeedsChart').getContext('2d');
        new Chart(ctx2, {
            type: 'radar',
            data: {
                labels: ['Cobol现代化', '开发效率', '成本控制', '合规要求', '技术创新', '风险管理'],
                datasets: [{
                    label: '需求紧迫性',
                    data: [95, 85, 80, 90, 75, 85],
                    backgroundColor: 'rgba(102, 126, 234, 0.2)',
                    borderColor: '#667eea',
                    borderWidth: 3,
                    pointBackgroundColor: '#667eea'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100,
                        ticks: { stepSize: 20 }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: '客户需求紧迫性分析 (0-100分)',
                        font: { size: 16, weight: 'bold' }
                    }
                }
            }
        });

        // ROI预测图表
        const ctx3 = document.getElementById('roiChart').getContext('2d');
        new Chart(ctx3, {
            type: 'line',
            data: {
                labels: ['Year 1', 'Year 2', 'Year 3', 'Year 4', 'Year 5'],
                datasets: [{
                    label: '累计收入 (亿日元)',
                    data: [15, 35, 80, 140, 200],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }, {
                    label: '累计投资 (亿日元)',
                    data: [20, 35, 50, 65, 75],
                    borderColor: '#764ba2',
                    backgroundColor: 'rgba(118, 75, 162, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                intersection: { mode: 'index' },
                plugins: {
                    title: {
                        display: true,
                        text: '5年期投资回报预测',
                        font: { size: 16, weight: 'bold' }
                    }
                },
                scales: {
                    y: { beginAtZero: true }
                }
            }
        });

        // 竞争对手雷达图
        const competitorChart = echarts.init(document.getElementById('competitorChart'));
        const competitorOption = {
            title: {
                text: '竞争对手能力对比分析',
                left: 'center',
                textStyle: { fontSize: 16, fontWeight: 'bold' }
            },
            tooltip: {},
            legend: {
                data: ['超自动化平台', 'IBM Watson', '富士通', 'Microsoft'],
                bottom: 0
            },
            radar: {
                indicator: [
                    { name: 'AI技术', max: 10 },
                    { name: 'Cobol专精', max: 10 },
                    { name: '市场份额', max: 10 },
                    { name: '本土化', max: 10 },
                    { name: '企业规模', max: 10 },
                    { name: '成本竞争力', max: 10 }
                ],
                radius: '60%'
            },
            series: [{
                type: 'radar',
                data: [
                    { value: [9.5, 9, 2, 7, 6, 8.5], name: '超自动化平台', itemStyle: { color: '#667eea' } },
                    { value: [8.5, 7, 8.5, 6.5, 9.5, 6], name: 'IBM Watson', itemStyle: { color: '#f5576c' } },
                    { value: [6.5, 9, 7, 9.5, 8.5, 7], name: '富士通', itemStyle: { color: '#4facfe' } },
                    { value: [7.5, 3, 9, 6, 9.5, 6.5], name: 'Microsoft', itemStyle: { color: '#f093fb' } }
                ]
            }]
        };
        competitorChart.setOption(competitorOption);

        // 响应式调整
        window.addEventListener('resize', function() {
            competitorChart.resize();
        });
    </script>

      <script>
        const createFloatingBall = ()=>{let a=document.createElement("style");a.textContent=`
          .fellou-floating-ball {
              position: fixed;
              bottom: 20px;
              right: 20px;
              background: #fff;
              border-radius: 50%;
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
              display: flex;
              gap: 8px;
              flex-direction: row;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              transition: all 0.15s ease;
              z-index: 100000;
              width: 28px;
              height: 28px;
              text-align: center;
              border: 2px solid #f4f4f4;
          }

          .fellou-floating-ball:hover {
              width: 140px;
              border-radius: 99px;
          }

          .fellou-floating-ball svg {
              width: 16px;
              height: 16px;
          }

          .fellou-floating-ball-text {
              display: none;
              width: 0px;
              transition: width 0.3s ease;
              color: #595561;
              font-family: Roboto;
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 20px;
              white-space: nowrap;
              overflow: hidden;
           
          }

          .fellou-floating-ball:hover .fellou-floating-ball-text {
              display: block;
              width: 100px;
          }
      `,document.head.appendChild(a);let b=document.createElement("div");b.className="fellou-floating-ball",b.addEventListener("click",()=>{window.open("https://fellou.ai","_blank")}),b.innerHTML=`
          <svg width="16" height="16" viewBox="0 0 152 152" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path opacity="0.72" d="M108.71 15.909C133.671 36.8541 134.337 77.1554 110.197 105.924C86.0565 134.694 46.2518 141.036 21.2904 120.091C-3.67096 99.1459 -4.33674 58.8446 19.8034 30.0755C43.9435 1.30644 83.7482 -5.03614 108.71 15.909ZM102.282 23.5694C81.8118 6.39315 48.2407 11.7425 27.4638 36.5034C6.68694 61.2643 7.24845 95.2543 27.7183 112.431C48.1882 129.607 81.7593 124.258 102.536 99.4966C123.313 74.7357 122.752 40.7457 102.282 23.5694Z" fill="url(#paint0_linear_34_1408)"/>
              <path d="M116.986 29.3811C141.525 49.9712 143.286 88.2698 120.921 114.924C98.5561 141.577 60.5333 146.493 35.995 125.903C11.4567 105.313 9.69493 67.0139 32.06 40.3602C54.4252 13.7065 92.4479 8.79095 116.986 29.3811ZM110.558 37.0415C90.3987 20.1255 58.6488 24.2301 39.7205 46.788C20.7921 69.346 22.2632 101.326 42.4229 118.242C62.5825 135.158 94.3324 131.054 113.261 108.496C132.189 85.9377 130.718 53.9574 110.558 37.0415Z" fill="url(#paint1_linear_34_1408)"/>
              <path d="M131.544 35.0694C155.71 55.3471 155.731 95.1074 131.591 123.876C107.451 152.646 68.291 159.529 44.1249 139.251C19.9589 118.974 19.9379 79.2135 44.078 50.4444C68.2182 21.6753 107.378 14.7917 131.544 35.0694ZM125.116 42.7299C105.505 26.2745 72.5526 32.067 51.7385 56.8723C30.9244 81.6776 30.9421 115.136 50.5528 131.591C70.1636 148.046 103.116 142.254 123.931 117.449C144.745 92.6433 144.727 59.1852 125.116 42.7299Z" fill="url(#paint2_linear_34_1408)"/>
              <defs>
                  <linearGradient id="paint0_linear_34_1408" x1="108.71" y1="15.909" x2="21.2904" y2="120.091" gradientUnits="userSpaceOnUse">
                      <stop stop-color="#6401F8" stop-opacity="0.7"/>
                      <stop offset="0.465" stop-color="#FF9000" stop-opacity="0.42"/>
                      <stop offset="1" stop-color="#33B3FF" stop-opacity="0.2"/>
                  </linearGradient>
                  <linearGradient id="paint1_linear_34_1408" x1="116.986" y1="29.381" x2="35.995" y2="125.903" gradientUnits="userSpaceOnUse">
                      <stop stop-color="#6401F8" stop-opacity="0.7"/>
                      <stop offset="0.465" stop-color="#FF9000" stop-opacity="0.42"/>
                      <stop offset="1" stop-color="#33B3FF" stop-opacity="0.2"/>
                  </linearGradient>
                  <linearGradient id="paint2_linear_34_1408" x1="131.544" y1="35.0694" x2="44.1249" y2="139.251" gradientUnits="userSpaceOnUse">
                      <stop stop-color="#6401F8"/>
                      <stop offset="0.5" stop-color="#FF9000"/>
                      <stop offset="1" stop-color="#33B3FF"/>
                  </linearGradient>
              </defs>
          </svg>
      `;let c=document.createElement("div");c.className="fellou-floating-ball-text",c.textContent="powered by fellou",b.appendChild(c),document.body.appendChild(b)};
        // Call createFloatingBall when the page loads
        window.addEventListener('load', createFloatingBall);
      </script>
    </body>
</html>